import React, { useState } from 'react';
import { PlusCircle, Check } from 'lucide-react';
import { MenuItem } from '../types';
import { useCart } from '../context/CartContext';

interface MenuCardProps {
  item: MenuItem;
}

const MenuCard: React.FC<MenuCardProps> = ({ item }) => {
  const { addToCart, cartItems } = useCart();
  const [isAdding, setIsAdding] = useState(false);

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(price);
  };

  const handleAddToCart = () => {
    addToCart(item);
    setIsAdding(true);
    setTimeout(() => setIsAdding(false), 1000);
  };

  // Check if item is in cart
  const itemInCart = cartItems.find(cartItem => cartItem.menuItem?.id === item.id);

  return (
    <div className="menu-card group">
      <div className="menu-card-img">
        <img
          src={item.image}
          alt={item.name}
          className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105"
        />
        {itemInCart && (
          <div className="absolute top-2 right-2 bg-primary-600 text-white text-xs font-semibold px-2 py-1 rounded-full">
            {itemInCart.quantity}x
          </div>
        )}
        <div className="absolute top-0 left-0 bg-accent-500 text-white px-3 py-1 text-xs font-medium rounded-br-lg">
          {item.category === 'food' ? 'Makanan' : 'Minuman'}
        </div>
      </div>
      <div className="menu-card-content">
        <h3 className="menu-card-title">{item.name}</h3>
        <p className="menu-card-desc">{item.description}</p>
        <div className="menu-card-footer">
          <span className="menu-card-price">{formatPrice(item.price)}</span>
          <button
            onClick={handleAddToCart}
            className={`menu-card-btn ${isAdding ? 'animate-pulse-once' : ''}`}
            aria-label={`Pesan ${item.name}`}
          >
            {isAdding ? (
              <>
                <Check size={18} className="text-success-500" />
                <span>Ditambahkan</span>
              </>
            ) : (
              <>
                <PlusCircle size={18} />
                <span>Pesan</span>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default MenuCard;