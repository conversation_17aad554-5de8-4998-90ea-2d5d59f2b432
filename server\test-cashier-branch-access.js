// Test script untuk verifikasi akses kasir cabang
// Kasir cabang hanya melihat produk dan kategori dari cabang mereka

const testCashierBranchAccess = () => {
  console.log('=== Cashier Branch Access Control Test ===\n');

  // Mock data
  const admin1 = { id: 1, role: 'admin', name: 'Admin 1' };
  const admin2 = { id: 2, role: 'admin', name: 'Admin 2' };
  
  const branches = [
    { id: 3, name: 'Branch A', user_id: 1 }, // Milik Admin 1
    { id: 4, name: 'Branch B', user_id: 1 }, // Milik Admin 1
    { id: 5, name: 'Branch C', user_id: 2 }, // Milik Admin 2
  ];

  const cashiers = [
    { id: 1, name: 'Direct Admin Cashier 1', user_id: 1, branch_id: null }, // Admin 1's direct cashier
    { id: 2, name: 'Branch A Cashier', user_id: 1, branch_id: 3 }, // Branch A cashier
    { id: 3, name: '<PERSON> B Cashier', user_id: 1, branch_id: 4 }, // Branch B cashier
    { id: 4, name: 'Branch C Cashier', user_id: 2, branch_id: 5 }, // Branch C cashier
  ];

  const products = [
    { id: 1, name: 'Admin 1 Product', user_id: 1, branch_id: null },
    { id: 2, name: 'Branch A Product', user_id: 1, branch_id: 3 },
    { id: 3, name: 'Branch A Product 2', user_id: 3, branch_id: 3 }, // Created by branch
    { id: 4, name: 'Branch B Product', user_id: 1, branch_id: 4 },
    { id: 5, name: 'Branch B Product 2', user_id: 4, branch_id: 4 }, // Created by branch
    { id: 6, name: 'Branch C Product', user_id: 2, branch_id: 5 },
    { id: 7, name: 'Admin 2 Product', user_id: 2, branch_id: null },
  ];

  const categories = [
    { id: 1, label: 'Admin 1 Category', user_id: 1 },
    { id: 2, label: 'Branch A Category', user_id: 3 }, // Created by branch A
    { id: 3, label: 'Branch B Category', user_id: 4 }, // Created by branch B
    { id: 4, label: 'Branch C Category', user_id: 5 }, // Created by branch C
    { id: 5, label: 'Admin 2 Category', user_id: 2 },
  ];

  // Test product access for cashiers
  const getProductsForCashier = (cashierId) => {
    const cashier = cashiers.find(c => c.id === cashierId);
    if (!cashier) return [];

    console.log(`\n--- Testing product access for cashier: ${cashier.name} ---`);
    
    let accessibleProducts = [];
    
    if (cashier.branch_id) {
      // Kasir cabang - hanya produk dari cabang tersebut
      accessibleProducts = products.filter(p => p.branch_id === cashier.branch_id);
      console.log(`Branch cashier can access products: [${accessibleProducts.map(p => p.name).join(', ')}]`);
    } else {
      // Kasir admin langsung - hanya produk admin (branch_id = null)
      accessibleProducts = products.filter(p => p.user_id === cashier.user_id && p.branch_id === null);
      console.log(`Direct admin cashier can access products: [${accessibleProducts.map(p => p.name).join(', ')}]`);
    }
    
    return accessibleProducts;
  };

  // Test category access for cashiers
  const getCategoriesForCashier = (cashierId) => {
    const cashier = cashiers.find(c => c.id === cashierId);
    if (!cashier) return [];

    console.log(`\n--- Testing category access for cashier: ${cashier.name} ---`);
    
    let accessibleCategories = [];
    
    if (cashier.branch_id) {
      // Kasir cabang - kategori dari cabang tersebut (user_id = branch_id)
      accessibleCategories = categories.filter(c => c.user_id === cashier.branch_id);
      console.log(`Branch cashier can access categories: [${accessibleCategories.map(c => c.label).join(', ')}]`);
    } else {
      // Kasir admin langsung - kategori admin
      accessibleCategories = categories.filter(c => c.user_id === cashier.user_id);
      console.log(`Direct admin cashier can access categories: [${accessibleCategories.map(c => c.label).join(', ')}]`);
    }
    
    return accessibleCategories;
  };

  // Test API parameter simulation
  const simulateAPICall = (cashierId) => {
    const cashier = cashiers.find(c => c.id === cashierId);
    if (!cashier) return;

    console.log(`\n--- Simulating API call for cashier: ${cashier.name} ---`);
    
    if (cashier.branch_id) {
      const branch = branches.find(b => b.id === cashier.branch_id);
      console.log(`✅ GET /api/products?user_id=${cashier.user_id}&cashier_id=${cashier.id}`);
      console.log(`   Backend detects: branch cashier, branch_id=${cashier.branch_id}`);
      console.log(`   Products Query: branch_id = ${cashier.branch_id}`);
      console.log(`   Categories Query: user_id = ${cashier.branch_id} (branch categories)`);
    } else {
      console.log(`✅ GET /api/products?user_id=${cashier.user_id}&cashier_id=${cashier.id}`);
      console.log(`   Backend detects: direct admin cashier`);
      console.log(`   Products Query: user_id = ${cashier.user_id} AND branch_id IS NULL`);
      console.log(`   Categories Query: user_id = ${cashier.user_id} (admin categories)`);
    }
  };

  // Test cross-branch isolation
  const testCrossBranchIsolation = () => {
    console.log('\n--- Testing Cross-Branch Isolation for Cashiers ---');
    
    const branchACashier = cashiers.find(c => c.branch_id === 3); // Branch A
    const branchBCashier = cashiers.find(c => c.branch_id === 4); // Branch B
    
    const branchAProducts = products.filter(p => p.branch_id === 3);
    const branchBProducts = products.filter(p => p.branch_id === 4);
    
    console.log(`Branch A Cashier can access ${branchAProducts.length} product(s): ${branchAProducts.map(p => p.name).join(', ')}`);
    console.log(`Branch A Cashier CANNOT access Branch B products: ${branchBProducts.map(p => p.name).join(', ')}`);
    
    const branchACategories = categories.filter(c => c.user_id === 3);
    const branchBCategories = categories.filter(c => c.user_id === 4);
    
    console.log(`Branch A Cashier can access ${branchACategories.length} category(ies): ${branchACategories.map(c => c.label).join(', ')}`);
    console.log(`Branch A Cashier CANNOT access Branch B categories: ${branchBCategories.map(c => c.label).join(', ')}`);
  };

  // Run tests
  console.log('1. PRODUCT ACCESS TESTS');
  getProductsForCashier(1); // Direct admin cashier
  getProductsForCashier(2); // Branch A cashier
  getProductsForCashier(3); // Branch B cashier
  getProductsForCashier(4); // Branch C cashier

  console.log('\n\n2. CATEGORY ACCESS TESTS');
  getCategoriesForCashier(1); // Direct admin cashier
  getCategoriesForCashier(2); // Branch A cashier
  getCategoriesForCashier(3); // Branch B cashier
  getCategoriesForCashier(4); // Branch C cashier

  console.log('\n\n3. API PARAMETER SIMULATION');
  simulateAPICall(1); // Direct admin cashier
  simulateAPICall(2); // Branch A cashier
  simulateAPICall(4); // Branch C cashier

  console.log('\n\n4. CROSS-BRANCH ISOLATION');
  testCrossBranchIsolation();

  console.log('\n=== Test Complete ===');
  console.log('\n📋 Expected Results:');
  console.log('✅ Branch cashiers only see products from their branch');
  console.log('✅ Branch cashiers only see categories from their branch');
  console.log('✅ Direct admin cashiers only see admin products/categories');
  console.log('✅ Complete isolation between different branches');
  console.log('✅ No access to admin products for branch cashiers');
  console.log('✅ No cross-branch data access');
};

// Run the test
testCashierBranchAccess();
