import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import { ArrowLeft, Save, Upload, X, Check } from 'lucide-react';
import config from '../config';
import { fetchWithSession, getCurrentUser } from '../utils/api';

interface StoreConfig {
  id: number;
  store_name: string;
  store_address: string;
  store_phone: string;
  store_email: string;
  tax_percentage: number;
  currency: string;
  logo: string;
  logo_url: string;
  receipt_footer: string;
  user_id?: number;
}

const StoreConfigPage: React.FC = () => {
  const [storeConfig, setStoreConfig] = useState<StoreConfig>({
    id: 1,
    store_name: '',
    store_address: '',
    store_phone: '',
    store_email: '',
    tax_percentage: 0,
    currency: 'IDR',
    logo: '',
    logo_url: '',
    receipt_footer: ''
  });

  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [showSuccessAlert, setShowSuccessAlert] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    fetchStoreConfig();
  }, []);

  const fetchStoreConfig = async () => {
    setIsLoading(true);
    try {
      // Dapatkan user_id dari user yang sedang login
      const currentUser = getCurrentUser();
      const userId = currentUser?.id;

      if (!userId) {
        return;
      }

      const response = await fetchWithSession(`${config.apiUrl}/store-config?user_id=${userId}`);
      if (response && response.ok) {
        const data = await response.json();
        setStoreConfig({
          ...data,
          user_id: userId // Pastikan user_id disimpan di state
        });
        if (data.logo_url) {
          setLogoPreview(data.logo_url);
        }
      }
    } catch (error) { } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setStoreConfig(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!storeConfig.store_name.trim()) {
      newErrors.store_name = 'Nama toko harus diisi';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setLogoFile(file);

      // Preview logo
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target && event.target.result) {
          setLogoPreview(event.target.result as string);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handleRemoveLogo = () => {
    setLogoPreview(null);
    setLogoFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    if (!validateForm()) {
      return;
    }

    setIsSaving(true);

    try {
      // Dapatkan user_id dari user yang sedang login
      const currentUser = getCurrentUser();
      const userId = currentUser?.id;

      if (!userId) {
        setIsSaving(false);
        return;
      }

      const formData = new FormData();
      formData.append('store_name', storeConfig.store_name);
      formData.append('store_address', storeConfig.store_address || '');
      formData.append('store_phone', storeConfig.store_phone || '');
      formData.append('store_email', storeConfig.store_email || '');
      formData.append('tax_percentage', storeConfig.tax_percentage.toString());
      formData.append('currency', storeConfig.currency);
      formData.append('receipt_footer', storeConfig.receipt_footer || '');
      formData.append('user_id', userId.toString());

      if (logoFile) {
        formData.append('logo', logoFile);
      }

      const response = await fetchWithSession(`${config.apiUrl}/store-config`, {
        method: 'PUT',
        body: formData
      });

      if (response && response.ok) {
        const updatedConfig = await response.json();
        setStoreConfig({
          ...updatedConfig,
          user_id: userId // Pastikan user_id tetap ada
        });
        if (updatedConfig.logo_url) {
          setLogoPreview(updatedConfig.logo_url);
        }
        setLogoFile(null);

        // Tampilkan pesan sukses
        setShowSuccessAlert(true);
        setTimeout(() => {
          setShowSuccessAlert(false);
        }, 3000);
      }
    } catch (error) { } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="container mx-auto p-4">
      <div className="flex items-center mb-6">
        <Link to="/" className="p-2 rounded-full hover:bg-neutral-100">
          <ArrowLeft size={20} />
        </Link>
        <h1 className="text-2xl font-bold text-primary-800 ml-2">Konfigurasi Toko</h1>
      </div>

      {showSuccessAlert && (
        <div className="mb-4 p-4 bg-green-100 border border-green-200 text-green-800 rounded-lg flex items-center">
          <Check size={20} className="mr-2" />
          <span>Konfigurasi toko berhasil disimpan!</span>
        </div>
      )}

      <div className="bg-white rounded-xl shadow-sm overflow-hidden">
        {isLoading ? (
          <div className="animate-pulse">
            <div className="h-12 bg-neutral-200 rounded mb-4"></div>
            <div className="h-32 bg-neutral-200 rounded mb-4"></div>
            <div className="h-12 bg-neutral-200 rounded mb-4"></div>
          </div>
        ) : (
          <div className="card">
            <form onSubmit={handleSubmit} className="p-6" autoComplete="off">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Logo Toko */}
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-neutral-700 mb-1">
                    Logo Toko
                  </label>
                  <div className="flex items-start">
                    <div className="mr-4">
                      {logoPreview ? (
                        <div className="relative">
                          <img
                            src={logoPreview}
                            alt="Logo Preview"
                            className="w-24 h-24 object-contain border rounded-lg"
                          />
                          <button
                            type="button"
                            onClick={handleRemoveLogo}
                            className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1"
                          >
                            <X className="h-4 w-4" />
                          </button>
                        </div>
                      ) : (
                        <div
                          onClick={() => fileInputRef.current?.click()}
                          className="w-24 h-24 border-2 border-dashed border-neutral-300 rounded-lg flex flex-col items-center justify-center cursor-pointer hover:border-primary-500 transition-colors"
                        >
                          <Upload className="h-8 w-8 text-neutral-400" />
                          <span className="text-xs text-neutral-500 mt-1">Upload</span>
                        </div>
                      )}
                    </div>
                    <div>
                      <button
                        type="button"
                        onClick={() => fileInputRef.current?.click()}
                        className="px-3 py-2 bg-neutral-100 text-neutral-700 rounded-md text-sm hover:bg-neutral-200 transition-colors mb-2"
                      >
                        Pilih File
                      </button>
                      <input
                        type="file"
                        ref={fileInputRef}
                        onChange={handleLogoChange}
                        className="hidden"
                        accept="image/*"
                        autoComplete="off"
                      />
                      <div className="text-sm text-neutral-500">
                        <p>Format: JPG, PNG, GIF</p>
                        <p>Ukuran maksimal: 2MB</p>
                        <p>Rekomendasi: 200x200 pixel</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Nama Toko */}
                <div>
                  <label htmlFor="store_name" className="block text-sm font-medium text-neutral-700 mb-1">
                    Nama Toko <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="store_name"
                    name="store_name"
                    value={storeConfig.store_name}
                    onChange={handleInputChange}
                    className={`w-full p-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 ${errors.store_name ? 'border-red-500' : ''}`}
                    required
                    autoComplete="off"
                  />
                  {errors.store_name && (
                    <p className="mt-1 text-sm text-red-600">{errors.store_name}</p>
                  )}
                </div>

                {/* Alamat Toko */}
                <div>
                  <label htmlFor="store_address" className="block text-sm font-medium text-neutral-700 mb-1">
                    Alamat Toko
                  </label>
                  <input
                    type="text"
                    id="store_address"
                    name="store_address"
                    value={storeConfig.store_address}
                    onChange={handleInputChange}
                    className="w-full p-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    autoComplete="off"
                  />
                </div>

                {/* Nomor Telepon */}
                <div>
                  <label htmlFor="store_phone" className="block text-sm font-medium text-neutral-700 mb-1">
                    Nomor Telepon
                  </label>
                  <input
                    type="text"
                    id="store_phone"
                    name="store_phone"
                    value={storeConfig.store_phone}
                    onChange={handleInputChange}
                    className="w-full p-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    autoComplete="off"
                  />
                </div>

                {/* Email */}
                <div>
                  <label htmlFor="store_email" className="block text-sm font-medium text-neutral-700 mb-1">
                    Email
                  </label>
                  <input
                    type="email"
                    id="store_email"
                    name="store_email"
                    value={storeConfig.store_email}
                    onChange={handleInputChange}
                    className="w-full p-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    autoComplete="off"
                  />
                </div>

                {/* Persentase Pajak */}
                <div>
                  <label htmlFor="tax_percentage" className="block text-sm font-medium text-neutral-700 mb-1">
                    Persentase Pajak (%)
                  </label>
                  <input
                    type="number"
                    id="tax_percentage"
                    name="tax_percentage"
                    value={storeConfig.tax_percentage}
                    onChange={handleInputChange}
                    min="0"
                    max="100"
                    step="0.1"
                    className="w-full p-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    autoComplete="off"
                  />
                </div>

                {/* Mata Uang */}
                <div>
                  <label htmlFor="currency" className="block text-sm font-medium text-neutral-700 mb-1">
                    Mata Uang
                  </label>
                  <input
                    type="text"
                    id="currency"
                    name="currency"
                    value={storeConfig.currency}
                    onChange={handleInputChange}
                    className="w-full p-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    autoComplete="off"
                  />
                </div>

                {/* Footer Struk */}
                <div className="md:col-span-2">
                  <label htmlFor="receipt_footer" className="block text-sm font-medium text-neutral-700 mb-1">
                    Footer Struk
                  </label>
                  <textarea
                    id="receipt_footer"
                    name="receipt_footer"
                    value={storeConfig.receipt_footer}
                    onChange={handleInputChange}
                    rows={3}
                    className="w-full p-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    autoComplete="off"
                  />
                  <p className="text-sm text-neutral-500 mt-1">
                    Teks ini akan muncul di bagian bawah struk pembayaran
                  </p>
                </div>
              </div>

              {/* Submit Button */}
              <div className="mt-6">
                <button
                  type="submit"
                  disabled={isSaving}
                  className={`w-full md:w-auto px-6 py-2 bg-primary-600 text-white rounded-md flex items-center justify-center ${isSaving ? 'opacity-70 cursor-not-allowed' : 'hover:bg-primary-700'
                    }`}
                >
                  {isSaving ? (
                    <>
                      <span className="mr-2">Menyimpan...</span>
                      <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></div>
                    </>
                  ) : (
                    <>
                      <Save className="h-5 w-5 mr-2" />
                      Simpan Pengaturan
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        )}
      </div>
    </div>
  );
};

export default StoreConfigPage;





