const express = require('express');
const router = express.Router();
const pool = require('../config/database');
const Cashier = require('../models/Cashier');

// Helper function to determine user role using user_type from session
async function getUserRole(user_id, user_type = null) {
  let userRole = null;

  // Use user_type from session to determine which table to check
  if (user_type === 'branch') {
    // Check branches table for branch users
    const [branchResult] = await pool.query('SELECT id FROM branches WHERE id = ? AND is_active = 1', [user_id]);
    if (branchResult.length > 0) {
      userRole = 'branch';
    }
  } else if (user_type === 'cashier') {
    // Check cashiers table for cashier users
    const [cashierResult] = await pool.query('SELECT id FROM cashiers WHERE id = ? AND is_active = 1', [user_id]);
    if (cashierResult.length > 0) {
      userRole = 'cashier';
    }
  } else if (user_type === 'msuser') {
    // Check msusers table for admin/other users
    const [userResult] = await pool.query('SELECT role FROM msusers WHERE id = ? AND is_active = 1', [user_id]);
    if (userResult.length > 0) {
      userRole = userResult[0].role;
    }
  } else {
    // Fallback: check all tables if user_type is not provided (backward compatibility)
    const [userResult] = await pool.query('SELECT role FROM msusers WHERE id = ? AND is_active = 1', [user_id]);
    if (userResult.length > 0) {
      userRole = userResult[0].role;
    } else {
      const [branchResult] = await pool.query('SELECT id FROM branches WHERE id = ? AND is_active = 1', [user_id]);
      if (branchResult.length > 0) {
        userRole = 'branch';
      } else {
        const [cashierResult] = await pool.query('SELECT id FROM cashiers WHERE id = ? AND is_active = 1', [user_id]);
        if (cashierResult.length > 0) {
          userRole = 'cashier';
        }
      }
    }
  }

  return userRole;
}

// Get all cashiers
router.get('/', async (req, res) => {
  try {
    const { user_id, branch_id, user_type } = req.query;

    if (!user_id) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Get user role to determine access level
    const userRole = await getUserRole(user_id, user_type);
    if (!userRole) {
      return res.status(404).json({ error: 'User not found' });
    }

    let query = `SELECT c.*, b.name as branch_name FROM ${Cashier.$table} c
                 LEFT JOIN branches b ON c.branch_id = b.id
                 WHERE c.is_active = 1`;
    const params = [];

    if (userRole === 'branch') {
      // Branch user - show cashiers from their branch AND cashiers created by their admin for this branch
      const [branchInfo] = await pool.query('SELECT user_id FROM branches WHERE id = ? AND is_active = 1', [user_id]);
      if (branchInfo.length > 0) {
        const adminId = branchInfo[0].user_id;
        query += ' AND (c.branch_id = ? OR (c.user_id = ? AND c.branch_id = ?))';
        params.push(user_id, adminId, user_id);
      } else {
        query += ' AND c.branch_id = ?';
        params.push(user_id);
      }
    } else if (userRole === 'admin' && branch_id) {
      // Admin user with specific branch filter
      query += ' AND c.branch_id = ?';
      params.push(branch_id);
    } else if (userRole === 'admin') {
      // Admin user without branch filter - show all their cashiers and branch cashiers
      query += ' AND (c.user_id = ? OR c.branch_id IN (SELECT id FROM branches WHERE user_id = ?))';
      params.push(user_id, user_id);
    }

    // Add sorting by name
    query += ' ORDER BY c.name ASC';

    const [cashiers] = await pool.query(query, params);
    res.json(cashiers);
  } catch (error) {
    console.error('Error getting cashiers:', error);
    res.status(500).json({ error: 'Failed to get cashiers' });
  }
});

// Get cashier by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { user_id, branch_id, user_type } = req.query;

    if (!user_id) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Get user role to determine access level
    const userRole = await getUserRole(user_id, user_type);
    if (!userRole) {
      return res.status(404).json({ error: 'User not found' });
    }

    let query = `SELECT c.*, b.name as branch_name FROM ${Cashier.$table} c
                 LEFT JOIN branches b ON c.branch_id = b.id
                 WHERE c.id = ? AND c.is_active = 1`;
    const params = [id];

    if (userRole === 'branch') {
      // User is a branch user - only show cashiers from this branch
      query += ' AND c.branch_id = ?';
      params.push(user_id);
    } else if (userRole === 'admin' && branch_id) {
      // Admin user with specific branch filter
      query += ' AND c.branch_id = ?';
      params.push(branch_id);
    } else if (userRole === 'admin') {
      // Admin user - show cashiers they own or from their branches
      query += ' AND (c.user_id = ? OR c.branch_id IN (SELECT id FROM branches WHERE user_id = ?))';
      params.push(user_id, user_id);
    } else {
      return res.status(403).json({ error: 'Access denied' });
    }

    const [cashiers] = await pool.query(query, params);

    if (cashiers.length === 0) {
      return res.status(404).json({ error: 'Cashier not found' });
    }

    res.json(cashiers[0]);
  } catch (error) {
    console.error('Error getting cashier:', error);
    res.status(500).json({ error: 'Failed to get cashier' });
  }
});

// Create new cashier
router.post('/', async (req, res) => {
  try {
    const { name, email, password, branch_id } = req.body;
    let { user_id } = req.body;

    if (!name || !email || !password || !user_id) {
      return res.status(400).json({ error: 'Name, email, password, and user_id are required' });
    }

    // Validate that name and password are not just spaces
    if (!name.trim()) {
      return res.status(400).json({ error: 'Nama kasir tidak boleh hanya berisi spasi' });
    }

    if (!password.trim()) {
      return res.status(400).json({ error: 'Password tidak boleh hanya berisi spasi' });
    }

    // Validasi email unik di cashiers
    const [existingCashiers] = await pool.query(
      `SELECT * FROM ${Cashier.$table} WHERE email = ? AND is_active = 1`,
      [email]
    );

    if (existingCashiers.length > 0) {
      return res.status(400).json({ error: 'Email sudah digunakan oleh kasir lain' });
    }

    // Validasi email unik di msusers
    const [existingUsers] = await pool.query(
      'SELECT id FROM msusers WHERE email = ?',
      [email]
    );

    if (existingUsers.length > 0) {
      return res.status(400).json({ error: 'Email sudah digunakan oleh pengguna' });
    }

    // Validasi email unik di branches
    const [existingBranches] = await pool.query(
      'SELECT id FROM branches WHERE email = ?',
      [email]
    );

    if (existingBranches.length > 0) {
      return res.status(400).json({ error: 'Email sudah digunakan oleh cabang' });
    }

    // Check if the user_id is actually a branch ID (branch users login with their branch ID as user_id)
    const [branchUser] = await pool.query(
      'SELECT * FROM branches WHERE id = ? AND is_active = 1',
      [user_id]
    );

    let finalBranchId = branch_id;
    let finalUserId = user_id;

    if (branchUser.length > 0) {
      // User is a branch user - use their branch ID and the actual user_id from branches table
      finalBranchId = branchUser[0].id;
      finalUserId = branchUser[0].user_id;
    } else if (branch_id) {
      // Admin user with branch_id - validate the branch belongs to this admin
      const [branches] = await pool.query(
        'SELECT * FROM branches WHERE id = ? AND user_id = ? AND is_active = 1',
        [branch_id, user_id]
      );

      if (branches.length === 0) {
        return res.status(400).json({ error: 'Invalid branch ID' });
      }
      finalBranchId = branch_id;
      finalUserId = user_id;
    } else {
      // Admin user without branch_id - direct admin cashier
      finalBranchId = null;
      finalUserId = user_id;
    }

    const data = {
      name,
      email,
      password,
      branch_id: finalBranchId || null,
      user_id: finalUserId,
      is_active: 1
    };

    const fields = Object.keys(data).join(', ');
    const placeholders = Object.keys(data).map(() => '?').join(', ');
    const values = Object.values(data);

    const [result] = await pool.query(
      `INSERT INTO ${Cashier.$table} (${fields}) VALUES (${placeholders})`,
      values
    );

    res.status(201).json({ id: result.insertId, ...data });
  } catch (error) {
    console.error('Error creating cashier:', error);
    res.status(500).json({ error: 'Failed to create cashier' });
  }
});

// Update cashier
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, email, password, branch_id, user_id } = req.body;

    if (!user_id) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Check if the user_id is actually a branch ID (branch users login with their branch ID as user_id)
    const [branchUser] = await pool.query(
      'SELECT * FROM branches WHERE id = ? AND is_active = 1',
      [user_id]
    );

    let authQuery;
    let authParams;

    if (branchUser.length > 0) {
      // User is a branch user - check if cashier belongs to this branch
      authQuery = `SELECT * FROM ${Cashier.$table} WHERE id = ? AND branch_id = ? AND is_active = 1`;
      authParams = [id, user_id];
    } else {
      // User is an admin - check if cashier belongs to admin or their branches
      authQuery = `SELECT * FROM ${Cashier.$table} WHERE id = ? AND
                   (user_id = ? OR branch_id IN (SELECT id FROM branches WHERE user_id = ?)) AND is_active = 1`;
      authParams = [id, user_id, user_id];
    }

    const [existingCashiers] = await pool.query(authQuery, authParams);

    if (existingCashiers.length === 0) {
      return res.status(404).json({ error: 'Cashier not found or not authorized' });
    }

    // Validate that name is not just spaces if provided
    if (name !== undefined && !name.trim()) {
      return res.status(400).json({ error: 'Nama kasir tidak boleh hanya berisi spasi' });
    }

    // Validate that password is not just spaces if provided
    if (password !== undefined && password !== '' && !password.trim()) {
      return res.status(400).json({ error: 'Password tidak boleh hanya berisi spasi' });
    }

    // Validasi email unik jika email diubah
    if (email && email !== existingCashiers[0].email) {
      // Validasi email unik di cashiers
      const [emailExists] = await pool.query(
        `SELECT * FROM ${Cashier.$table} WHERE email = ? AND id != ? AND is_active = 1`,
        [email, id]
      );

      if (emailExists.length > 0) {
        return res.status(400).json({ error: 'Email sudah digunakan oleh kasir lain' });
      }

      // Validasi email unik di msusers
      const [userExists] = await pool.query(
        'SELECT id FROM msusers WHERE email = ?',
        [email]
      );
      if (userExists.length > 0) {
        return res.status(400).json({ error: 'Email sudah digunakan oleh pengguna' });
      }

      // Validasi email unik di branches
      const [branchExists] = await pool.query(
        'SELECT id FROM branches WHERE email = ?',
        [email]
      );
      if (branchExists.length > 0) {
        return res.status(400).json({ error: 'Email sudah digunakan oleh cabang' });
      }
    }

    // Validasi branch_id hanya untuk admin user, bukan branch user
    if (branch_id && branchUser.length === 0) {
      // Hanya validasi jika user adalah admin (bukan branch user)
      const [branches] = await pool.query(
        'SELECT * FROM branches WHERE id = ? AND user_id = ? AND is_active = 1',
        [branch_id, user_id]
      );

      if (branches.length === 0) {
        return res.status(400).json({ error: 'Invalid branch ID' });
      }
    }

    // Buat objek data untuk update
    const data = {};
    if (name) data.name = name;
    if (email) data.email = email;
    if (password) data.password = password;
    if (branch_id !== undefined) data.branch_id = branch_id || null;

    // Jika tidak ada data yang diupdate
    if (Object.keys(data).length === 0) {
      return res.status(400).json({ error: 'No data to update' });
    }

    const setClause = Object.keys(data).map(key => `${key} = ?`).join(', ');
    const values = [...Object.values(data), id];

    const [result] = await pool.query(
      `UPDATE ${Cashier.$table} SET ${setClause} WHERE id = ?`,
      values
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'Cashier not found' });
    }

    res.json({ id, ...data });
  } catch (error) {
    console.error('Error updating cashier:', error);
    res.status(500).json({ error: 'Failed to update cashier' });
  }
});

// Delete cashier (soft delete)
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { user_id } = req.query;

    if (!user_id) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Validasi kasir ada dan milik user atau cabangnya
    const [existingCashiers] = await pool.query(
      `SELECT * FROM ${Cashier.$table} WHERE id = ? AND 
       (user_id = ? OR branch_id = ?) AND is_active = 1`,
      [id, user_id, user_id]
    );

    if (existingCashiers.length === 0) {
      return res.status(404).json({ error: 'Cashier not found or not authorized' });
    }

    const [result] = await pool.query(
      `UPDATE ${Cashier.$table} SET is_active = 0 WHERE id = ?`,
      [id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'Cashier not found' });
    }

    res.json({ message: 'Cashier deleted successfully' });
  } catch (error) {
    console.error('Error deleting cashier:', error);
    res.status(500).json({ error: 'Failed to delete cashier' });
  }
});

module.exports = router;

