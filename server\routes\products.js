const express = require('express');
const router = express.Router();
const pool = require('../config/database');
const Product = require('../models/Product');

// Helper function to determine user role using user_type from session
async function getUserRole(user_id, user_type = null) {
  let userRole = null;

  // Use user_type from session to determine which table to check
  if (user_type === 'branch') {
    // Check branches table for branch users
    const [branchResult] = await pool.query('SELECT id FROM branches WHERE id = ? AND is_active = 1', [user_id]);
    if (branchResult.length > 0) {
      userRole = 'branch';
    }
  } else if (user_type === 'cashier') {
    // Check cashiers table for cashier users
    const [cashierResult] = await pool.query('SELECT id FROM cashiers WHERE id = ? AND is_active = 1', [user_id]);
    if (cashierResult.length > 0) {
      userRole = 'cashier';
    }
  } else if (user_type === 'msuser') {
    // Check msusers table for admin/other users
    const [userResult] = await pool.query('SELECT role FROM msusers WHERE id = ? AND is_active = 1', [user_id]);
    if (userResult.length > 0) {
      userRole = userResult[0].role;
    }
  } else {
    // Fallback: check all tables if user_type is not provided (backward compatibility)
    const [userResult] = await pool.query('SELECT role FROM msusers WHERE id = ? AND is_active = 1', [user_id]);
    if (userResult.length > 0) {
      userRole = userResult[0].role;
    } else {
      const [branchResult] = await pool.query('SELECT id FROM branches WHERE id = ? AND is_active = 1', [user_id]);
      if (branchResult.length > 0) {
        userRole = 'branch';
      } else {
        const [cashierResult] = await pool.query('SELECT id FROM cashiers WHERE id = ? AND is_active = 1', [user_id]);
        if (cashierResult.length > 0) {
          userRole = 'cashier';
        }
      }
    }
  }

  return userRole;
}

// Get all products
router.get('/', async (req, res) => {
  try {
    let { user_id, cashier_id, user_type } = req.query;
    let { branch_id } = req.query;
    let original_user_id = user_id;

    // Jika request dari kasir, tentukan branch_id dan admin_id
    if (cashier_id) {
      const [cashierResult] = await pool.query(
        'SELECT c.id AS cashier_id, c.user_id, c.branch_id, b.user_id as admin_id FROM cashiers c LEFT JOIN branches b ON c.branch_id = b.id WHERE c.id = ? AND c.is_active = 1',
        [cashier_id]
      );

      if (cashierResult.length === 0) {
        return res.status(404).json({ error: 'Cashier not found' });
      }

      branch_id = cashierResult[0].branch_id;
      // Override user_id with admin_id for product filtering
      if (cashierResult[0].user_id) {
        user_id = cashierResult[0].user_id;
      }
    }

    if (!user_id) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Get user role to determine access level
    const userRole = await getUserRole(original_user_id, user_type);

    if (!userRole) {
      return res.status(404).json({ error: 'User not found' });
    }

    let query = `SELECT a.* FROM ${Product.$table} a JOIN categories b ON b.value = a.category AND ((b.user_id = a.user_id AND a.branch_id IS NULL) OR (b.user_id = a.branch_id AND a.branch_id IS NOT NULL)) WHERE a.is_active = 1`;
    const params = [];

    if (cashier_id) {
      // Request from cashier
      if (branch_id) {
        // Cashier milik cabang - hanya tampilkan produk dari cabang tersebut
        query += ` AND a.branch_id = ?`;
        params.push(branch_id);
      } else {
        // Cashier tanpa cabang (direct admin cashier) - show all admin products
        query += ` AND a.user_id = ? AND a.branch_id IS NULL`;
        params.push(user_id);
      }
    } else if (userRole === 'branch') {
      // User is a branch user - only show products they created themselves
      query += ` AND a.branch_id = ?`;
      params.push(user_id);
    } else if (userRole === 'admin' && branch_id) {
      // Admin user with specific branch filter - show products from that branch and admin products
      const [branchResult] = await pool.query(
        'SELECT user_id FROM branches WHERE id = ? AND user_id = ?',
        [branch_id, user_id]
      );

      if (branchResult.length === 0) {
        return res.status(403).json({ error: 'Access denied to this branch' });
      }

      query += ` AND (a.branch_id = ? OR (a.user_id = ? AND a.branch_id IS NULL))`;
      params.push(branch_id, user_id);
    } else if (userRole === 'admin') {
      // Admin user without branch filter - show all products they have access to
      // This includes: products they created directly (user_id = admin_id)
      // AND products from branches they own (branch_id IN their branches)
      query += ` AND (a.user_id = ? OR a.branch_id IN (SELECT id FROM branches WHERE user_id = ?))`;
      params.push(user_id, user_id);
    } else if (userRole === 'cashier') {
      // Cashier access - should be handled above, but fallback
      query += ` AND a.user_id = ?`;
      params.push(user_id);
    } else {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Add sorting by name
    query += ` ORDER BY a.name ASC`;

    console.log('Product Query:', query);
    console.log('Product Params:', params);

    const [products] = await pool.query(query, params);
    res.json(products);
  } catch (error) {
    console.error('Error fetching products:', error);
    res.status(500).json({ error: 'Failed to fetch products' });
  }
});

// Get product by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { user_id, user_type } = req.query;

    if (!user_id) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Get user role to determine access level
    const userRole = await getUserRole(user_id, user_type);
    if (!userRole) {
      return res.status(404).json({ error: 'User not found' });
    }

    let query = `SELECT * FROM ${Product.$table} WHERE id = ? AND is_active = 1`;
    const params = [id];

    if (userRole === 'branch') {
      // Branch user - only allow access to products they created themselves
      query += ` AND branch_id = ?`;
      params.push(user_id);
    } else if (userRole === 'admin') {
      // Admin user - allow access to their products and their branch products
      query += ` AND (user_id = ? OR branch_id IN (SELECT id FROM branches WHERE user_id = ?))`;
      params.push(user_id, user_id);
    } else if (userRole === 'cashier') {
      // Cashier - show products based on their branch
      const [cashierInfo] = await pool.query('SELECT branch_id FROM cashiers WHERE id = ?', [user_id]);
      if (cashierInfo.length > 0 && cashierInfo[0].branch_id) {
        query += ` AND (branch_id = ? OR user_id = (SELECT user_id FROM branches WHERE id = ?))`;
        params.push(cashierInfo[0].branch_id, cashierInfo[0].branch_id);
      } else {
        // Cashier without branch - show admin products
        query += ` AND user_id = 1`;
      }
    } else {
      return res.status(403).json({ error: 'Access denied' });
    }

    const [products] = await pool.query(query, params);

    if (products.length === 0) {
      return res.status(404).json({ error: 'Product not found or access denied' });
    }

    res.json(products[0]);
  } catch (error) {
    console.error('Error fetching product:', error);
    res.status(500).json({ error: 'Failed to fetch product' });
  }
});

// Create new product
router.post('/', async (req, res) => {
  try {
    const { name, price, cost_price, image, category, category_label, user_id } = req.body;

    // Validasi input
    if (!name || !price || !image || !category || !user_id) {
      return res.status(400).json({ error: 'All fields are required' });
    }

    // Check if user is a branch user
    const [branchUser] = await pool.query(
      'SELECT * FROM branches WHERE id = ? AND is_active = 1',
      [user_id]
    );

    let branch_id = null;
    let actual_user_id = user_id;

    if (branchUser.length > 0) {
      // User is a branch user - set branch_id and use admin's user_id
      branch_id = user_id;
      actual_user_id = branchUser[0].user_id;
    }

    const [result] = await pool.query(
      `INSERT INTO ${Product.$table} (name, price, cost_price, image, category, category_label, user_id, branch_id, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1)`,
      [name, price, cost_price || 0, image, category, category_label, actual_user_id, branch_id]
    );

    res.status(201).json({
      id: result.insertId,
      name,
      price,
      cost_price,
      image,
      category,
      category_label,
      user_id: actual_user_id,
      branch_id,
      is_active: 1
    });
  } catch (error) {
    console.error('Error creating product:', error);
    res.status(500).json({ error: 'Failed to create product' });
  }
});

// Update product
router.put('/:id', async (req, res) => {
  try {
    const { name, price, cost_price, image, category, category_label, user_id, user_type } = req.body;
    const productId = req.params.id;

    // Validasi input
    if (!name || !price || !image || !category || !user_id) {
      return res.status(400).json({ error: 'All fields are required' });
    }

    // Get user role to determine access level
    const userRole = await getUserRole(user_id, user_type);
    if (!userRole) {
      return res.status(404).json({ error: 'User not found' });
    }

    let authQuery;
    let authParams;

    if (userRole === 'branch') {
      // Branch user - can only update products from their branch
      authQuery = `SELECT * FROM ${Product.$table} WHERE id = ? AND is_active = 1 AND branch_id = ?`;
      authParams = [productId, user_id];
    } else if (userRole === 'admin') {
      // Admin user - can update any product (their products and their branch products)
      authQuery = `SELECT * FROM ${Product.$table} WHERE id = ? AND is_active = 1 AND (user_id = ? OR branch_id IN (SELECT id FROM branches WHERE user_id = ?))`;
      authParams = [productId, user_id, user_id];
    } else if (userRole === 'cashier') {
      // Cashiers cannot update products
      return res.status(403).json({ error: 'Cashiers are not authorized to update products' });
    } else {
      return res.status(403).json({ error: 'Access denied' });
    }

    const [products] = await pool.query(authQuery, authParams);

    if (products.length === 0) {
      return res.status(404).json({ error: 'Product not found or not authorized' });
    }

    await pool.query(
      `UPDATE ${Product.$table} SET name = ?, price = ?, cost_price = ?, image = ?, category = ?, category_label = ? WHERE id = ?`,
      [name, price, cost_price || 0, image, category, category_label, productId]
    );

    const product = products[0];
    res.json({
      id: productId,
      name,
      price,
      cost_price,
      image,
      category,
      category_label,
      user_id: product.user_id,
      branch_id: product.branch_id,
      is_active: 1
    });
  } catch (error) {
    console.error('Error updating product:', error);
    res.status(500).json({ error: 'Failed to update product' });
  }
});

// Delete product (soft delete)
router.delete('/:id', async (req, res) => {
  try {
    const productId = req.params.id;
    const { user_id, user_type } = req.query;

    if (!user_id) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Get user role to determine access level
    const userRole = await getUserRole(user_id, user_type);
    if (!userRole) {
      return res.status(404).json({ error: 'User not found' });
    }

    let authQuery;
    let authParams;

    if (userRole === 'branch') {
      // Branch user - can only delete products from their branch
      authQuery = `SELECT * FROM ${Product.$table} WHERE id = ? AND is_active = 1 AND branch_id = ?`;
      authParams = [productId, user_id];
    } else if (userRole === 'admin') {
      // Admin user - can delete any product (their products and their branch products)
      authQuery = `SELECT * FROM ${Product.$table} WHERE id = ? AND is_active = 1 AND (user_id = ? OR branch_id IN (SELECT id FROM branches WHERE user_id = ?))`;
      authParams = [productId, user_id, user_id];
    } else if (userRole === 'cashier') {
      // Cashiers cannot delete products
      return res.status(403).json({ error: 'Cashiers are not authorized to delete products' });
    } else {
      return res.status(403).json({ error: 'Access denied' });
    }

    const [products] = await pool.query(authQuery, authParams);

    if (products.length === 0) {
      return res.status(404).json({ error: 'Product not found or not authorized' });
    }

    await pool.query(
      `UPDATE ${Product.$table} SET is_active = 0 WHERE id = ?`,
      [productId]
    );

    res.json({ message: 'Product deleted successfully' });
  } catch (error) {
    console.error('Error deleting product:', error);
    res.status(500).json({ error: 'Failed to delete product' });
  }
});

module.exports = router;

