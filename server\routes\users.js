const express = require('express');
const router = express.Router();
const pool = require('../config/database');

// Get all users
router.get('/', async (req, res) => {
  try {
    const [rows] = await pool.query('SELECT id, name, email, role, is_active, created_at, updated_at FROM msusers');
    res.json(rows);
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({ error: 'Failed to fetch users' });
  }
});

// Get user by ID
router.get('/:id', async (req, res) => {
  try {
    const [rows] = await pool.query(
      'SELECT id, name, email, role, is_active, created_at, updated_at FROM msusers WHERE id = ?',
      [req.params.id]
    );

    if (rows.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json(rows[0]);
  } catch (error) {
    console.error('Error fetching user:', error);
    res.status(500).json({ error: 'Failed to fetch user' });
  }
});

// Create new user
router.post('/', async (req, res) => {
  const { name, email, password, role, is_active } = req.body;

  if (!name || !email || !password || !role) {
    return res.status(400).json({ error: 'All fields are required' });
  }

  try {
    // Cek apakah email sudah digunakan di tabel msusers
    const [existingUsers] = await pool.query(
      'SELECT id FROM msusers WHERE email = ?',
      [email]
    );

    if (existingUsers.length > 0) {
      return res.status(400).json({ error: 'Email sudah digunakan oleh pengguna lain' });
    }

    // Cek apakah email sudah digunakan di tabel branches
    const [existingBranches] = await pool.query(
      'SELECT id FROM branches WHERE email = ?',
      [email]
    );

    if (existingBranches.length > 0) {
      return res.status(400).json({ error: 'Email sudah digunakan oleh cabang' });
    }

    // Cek apakah email sudah digunakan di tabel cashiers
    const [existingCashiers] = await pool.query(
      'SELECT id FROM cashiers WHERE email = ? AND is_active = 1',
      [email]
    );

    if (existingCashiers.length > 0) {
      return res.status(400).json({ error: 'Email sudah digunakan oleh kasir' });
    }

    const [result] = await pool.query(
      'INSERT INTO msusers (name, email, password, role, is_active) VALUES (?, ?, ?, ?, ?)',
      [name, email, password, role, is_active ? 1 : 0]
    );

    const userData = {
      id: result.insertId,
      name,
      email,
      role,
      is_active: is_active ? 1 : 0
    };

    res.status(201).json(userData);
  } catch (error) {
    console.error('Error creating user:', error);
    res.status(500).json({ error: 'Failed to create user' });
  }
});

// Update user
router.put('/:id', async (req, res) => {
  const { name, email, password, role, is_active } = req.body;

  if (!name || !email || !role) {
    return res.status(400).json({ error: 'Required fields missing' });
  }

  try {
    // Cek apakah email sudah digunakan oleh pengguna lain
    const [existingUsers] = await pool.query(
      'SELECT id FROM msusers WHERE email = ? AND id != ?',
      [email, req.params.id]
    );

    if (existingUsers.length > 0) {
      return res.status(400).json({ error: 'Email sudah digunakan oleh pengguna lain' });
    }

    // Cek apakah email sudah digunakan di tabel branches
    const [existingBranches] = await pool.query(
      'SELECT id FROM branches WHERE email = ?',
      [email]
    );

    if (existingBranches.length > 0) {
      return res.status(400).json({ error: 'Email sudah digunakan oleh cabang' });
    }

    // Cek apakah email sudah digunakan di tabel cashiers
    const [existingCashiers] = await pool.query(
      'SELECT id FROM cashiers WHERE email = ? AND is_active = 1',
      [email]
    );

    if (existingCashiers.length > 0) {
      return res.status(400).json({ error: 'Email sudah digunakan oleh kasir' });
    }

    const passwordQuery = password ? ', password = ?' : '';
    const params = [name, email, role, is_active ? 1 : 0];

    if (password) params.push(password);
    params.push(req.params.id);

    await pool.query(
      `UPDATE msusers SET name = ?, email = ?, role = ?, is_active = ?${passwordQuery} WHERE id = ?`,
      params
    );

    const userData = {
      id: parseInt(req.params.id),
      name,
      email,
      role,
      is_active: is_active ? 1 : 0
    };

    res.json(userData);
  } catch (error) {
    console.error('Error updating user:', error);
    res.status(500).json({ error: 'Failed to update user' });
  }
});

// Delete user
router.delete('/:id', async (req, res) => {
  try {
    await pool.query('DELETE FROM msusers WHERE id = ?', [req.params.id]);
    res.json({ message: 'User deleted successfully' });
  } catch (error) {
    console.error('Error deleting user:', error);
    res.status(500).json({ error: 'Failed to delete user' });
  }
});

module.exports = router;
