const pool = require('../config/database');

/**
 * Helper function to determine user role using user_type from session
 * This provides a centralized way to validate users across different tables
 * and eliminates ambiguity between msusers, branches, and cashiers tables
 * 
 * @param {string|number} user_id - The user ID to validate
 * @param {string} user_type - The user type from session ('msuser', 'branch', 'cashier')
 * @returns {string|null} - The user role or null if user not found
 */
async function getUserRole(user_id, user_type = null) {
  let userRole = null;

  try {
    // Use user_type from session to determine which table to check
    if (user_type === 'branch') {
      // Check branches table for branch users
      const [branchResult] = await pool.query('SELECT id FROM branches WHERE id = ? AND is_active = 1', [user_id]);
      if (branchResult.length > 0) {
        userRole = 'branch';
      }
    } else if (user_type === 'cashier') {
      // Check cashiers table for cashier users
      const [cashierResult] = await pool.query('SELECT id FROM cashiers WHERE id = ? AND is_active = 1', [user_id]);
      if (cashierResult.length > 0) {
        userRole = 'cashier';
      }
    } else if (user_type === 'msuser') {
      // Check msusers table for admin/other users
      const [userResult] = await pool.query('SELECT role FROM msusers WHERE id = ? AND is_active = 1', [user_id]);
      if (userResult.length > 0) {
        userRole = userResult[0].role;
      }
    } else {
      // Fallback: check all tables if user_type is not provided (backward compatibility)
      const [userResult] = await pool.query('SELECT role FROM msusers WHERE id = ? AND is_active = 1', [user_id]);
      if (userResult.length > 0) {
        userRole = userResult[0].role;
      } else {
        const [branchResult] = await pool.query('SELECT id FROM branches WHERE id = ? AND is_active = 1', [user_id]);
        if (branchResult.length > 0) {
          userRole = 'branch';
        } else {
          const [cashierResult] = await pool.query('SELECT id FROM cashiers WHERE id = ? AND is_active = 1', [user_id]);
          if (cashierResult.length > 0) {
            userRole = 'cashier';
          }
        }
      }
    }
  } catch (error) {
    console.error('Error in getUserRole:', error);
    return null;
  }

  return userRole;
}

/**
 * Middleware function to validate user role and add it to request object
 * Usage: router.get('/endpoint', validateUserRole, (req, res) => { ... })
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function validateUserRole(req, res, next) {
  try {
    const { user_id, user_type } = req.query.user_id ? req.query : req.body;
    
    if (!user_id) {
      return res.status(400).json({ error: 'user_id is required' });
    }

    const userRole = await getUserRole(user_id, user_type);
    if (!userRole) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Add user role to request object for use in route handlers
    req.userRole = userRole;
    req.userId = user_id;
    req.userType = user_type;
    
    next();
  } catch (error) {
    console.error('Error in validateUserRole middleware:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * Get user information with role validation
 * 
 * @param {string|number} user_id - The user ID
 * @param {string} user_type - The user type from session
 * @returns {Object|null} - User information object or null if not found
 */
async function getUserInfo(user_id, user_type = null) {
  try {
    if (user_type === 'branch') {
      const [branchResult] = await pool.query('SELECT * FROM branches WHERE id = ? AND is_active = 1', [user_id]);
      if (branchResult.length > 0) {
        return { ...branchResult[0], role: 'branch', user_type: 'branch' };
      }
    } else if (user_type === 'cashier') {
      const [cashierResult] = await pool.query('SELECT * FROM cashiers WHERE id = ? AND is_active = 1', [user_id]);
      if (cashierResult.length > 0) {
        return { ...cashierResult[0], role: 'cashier', user_type: 'cashier' };
      }
    } else if (user_type === 'msuser') {
      const [userResult] = await pool.query('SELECT * FROM msusers WHERE id = ? AND is_active = 1', [user_id]);
      if (userResult.length > 0) {
        return { ...userResult[0], user_type: 'msuser' };
      }
    }
    
    return null;
  } catch (error) {
    console.error('Error in getUserInfo:', error);
    return null;
  }
}

module.exports = {
  getUserRole,
  validateUserRole,
  getUserInfo
};
