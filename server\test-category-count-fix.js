// Test script untuk verifikasi perbaikan count kategori
// Memastikan count kategori "All Menu" sesuai dengan produk yang bisa diakses kasir

const testCategoryCountFix = () => {
  console.log('=== Category Count Fix Test ===\n');

  // Mock data
  const admin1 = { id: 1, role: 'admin', name: 'Admin 1' };
  const admin2 = { id: 2, role: 'admin', name: 'Admin 2' };
  
  const branches = [
    { id: 3, name: 'Branch A', user_id: 1 }, // Milik Admin 1
    { id: 4, name: 'Branch B', user_id: 1 }, // Milik Admin 1
    { id: 5, name: 'Branch C', user_id: 2 }, // Milik Admin 2
  ];

  const cashiers = [
    { id: 1, name: 'Direct Admin Cashier 1', user_id: 1, branch_id: null }, // Admin 1's direct cashier
    { id: 2, name: 'Branch A Cashier', user_id: 1, branch_id: 3 }, // Branch A cashier
    { id: 3, name: '<PERSON> B Cashier', user_id: 1, branch_id: 4 }, // Branch B cashier
    { id: 4, name: 'Branch C Cashier', user_id: 2, branch_id: 5 }, // Branch C cashier
  ];

  const products = [
    // Admin 1 products
    { id: 1, name: 'Admin 1 Product 1', user_id: 1, branch_id: null, category: 'food', is_active: 1 },
    { id: 2, name: 'Admin 1 Product 2', user_id: 1, branch_id: null, category: 'drink', is_active: 1 },
    
    // Branch A products (total: 3)
    { id: 3, name: 'Branch A Product 1', user_id: 1, branch_id: 3, category: 'food', is_active: 1 },
    { id: 4, name: 'Branch A Product 2', user_id: 3, branch_id: 3, category: 'food', is_active: 1 },
    { id: 5, name: 'Branch A Product 3', user_id: 3, branch_id: 3, category: 'drink', is_active: 1 },
    
    // Branch B products (total: 2)
    { id: 6, name: 'Branch B Product 1', user_id: 1, branch_id: 4, category: 'food', is_active: 1 },
    { id: 7, name: 'Branch B Product 2', user_id: 4, branch_id: 4, category: 'drink', is_active: 1 },
    
    // Branch C products (total: 4)
    { id: 8, name: 'Branch C Product 1', user_id: 2, branch_id: 5, category: 'food', is_active: 1 },
    { id: 9, name: 'Branch C Product 2', user_id: 5, branch_id: 5, category: 'food', is_active: 1 },
    { id: 10, name: 'Branch C Product 3', user_id: 5, branch_id: 5, category: 'drink', is_active: 1 },
    { id: 11, name: 'Branch C Product 4', user_id: 5, branch_id: 5, category: 'snack', is_active: 1 },
    
    // Admin 2 products
    { id: 12, name: 'Admin 2 Product 1', user_id: 2, branch_id: null, category: 'food', is_active: 1 },
    { id: 13, name: 'Admin 2 Product 2', user_id: 2, branch_id: null, category: 'drink', is_active: 1 },
    { id: 14, name: 'Admin 2 Product 3', user_id: 2, branch_id: null, category: 'snack', is_active: 1 },
    
    // Inactive products (should not be counted)
    { id: 15, name: 'Inactive Product 1', user_id: 1, branch_id: 3, category: 'food', is_active: 0 },
    { id: 16, name: 'Inactive Product 2', user_id: 2, branch_id: 5, category: 'drink', is_active: 0 },
  ];

  // Test product count for different users
  const getProductCountForUser = (cashierId) => {
    const cashier = cashiers.find(c => c.id === cashierId);
    if (!cashier) return { total: 0, byCategory: {} };

    console.log(`\n--- Testing product count for cashier: ${cashier.name} ---`);
    
    let accessibleProducts = [];
    
    if (cashier.branch_id) {
      // Kasir cabang - hanya produk dari cabang tersebut
      accessibleProducts = products.filter(p => 
        p.branch_id === cashier.branch_id && p.is_active === 1
      );
    } else {
      // Kasir admin langsung - hanya produk admin (branch_id = null)
      accessibleProducts = products.filter(p => 
        p.user_id === cashier.user_id && p.branch_id === null && p.is_active === 1
      );
    }
    
    // Count by category
    const byCategory = {};
    accessibleProducts.forEach(product => {
      byCategory[product.category] = (byCategory[product.category] || 0) + 1;
    });
    
    const total = accessibleProducts.length;
    
    console.log(`Total products accessible: ${total}`);
    console.log(`Products by category:`, byCategory);
    console.log(`Product names: [${accessibleProducts.map(p => p.name).join(', ')}]`);
    
    return { total, byCategory, products: accessibleProducts };
  };

  // Test category count calculation
  const testCategoryCount = (cashierId) => {
    const cashier = cashiers.find(c => c.id === cashierId);
    if (!cashier) return;

    console.log(`\n--- Testing category count for cashier: ${cashier.name} ---`);
    
    const { total, byCategory } = getProductCountForUser(cashierId);
    
    console.log(`Expected "All Menu" count: ${total}`);
    console.log(`Expected category counts:`);
    Object.entries(byCategory).forEach(([category, count]) => {
      console.log(`  - ${category}: ${count}`);
    });
    
    // Simulate API response
    const categories = [
      { value: 'all', label: 'All Menu', count: total },
      { value: 'food', label: 'Food', count: byCategory.food || 0 },
      { value: 'drink', label: 'Drink', count: byCategory.drink || 0 },
      { value: 'snack', label: 'Snack', count: byCategory.snack || 0 },
    ];
    
    console.log(`API Response should be:`, categories);
    
    return categories;
  };

  // Test API parameter simulation
  const simulateAPICall = (cashierId) => {
    const cashier = cashiers.find(c => c.id === cashierId);
    if (!cashier) return;

    console.log(`\n--- Simulating API call for cashier: ${cashier.name} ---`);
    
    if (cashier.branch_id) {
      console.log(`✅ GET /api/categories?user_id=${cashier.user_id}&cashier_id=${cashier.id}`);
      console.log(`   Backend detects: branch cashier, branch_id=${cashier.branch_id}`);
      console.log(`   Count Query: branch_id = ${cashier.branch_id} AND is_active = 1`);
    } else {
      console.log(`✅ GET /api/categories?user_id=${cashier.user_id}&cashier_id=${cashier.id}`);
      console.log(`   Backend detects: direct admin cashier`);
      console.log(`   Count Query: user_id = ${cashier.user_id} AND branch_id IS NULL AND is_active = 1`);
    }
  };

  // Test before/after comparison
  const testBeforeAfterComparison = () => {
    console.log('\n--- Before/After Comparison ---');
    
    const branchACashier = cashiers.find(c => c.branch_id === 3);
    const { total: branchACount } = getProductCountForUser(branchACashier.id);
    
    // Simulate old behavior (counting all products)
    const allActiveProducts = products.filter(p => p.is_active === 1);
    const oldCount = allActiveProducts.length;
    
    console.log(`\nBranch A Cashier:`);
    console.log(`❌ OLD: "All Menu" shows ${oldCount} products (from all branches)`);
    console.log(`✅ NEW: "All Menu" shows ${branchACount} products (only from Branch A)`);
    console.log(`🎯 IMPROVEMENT: Reduced from ${oldCount} to ${branchACount} products (${((oldCount - branchACount) / oldCount * 100).toFixed(1)}% reduction)`);
  };

  // Run tests
  console.log('1. PRODUCT COUNT TESTS');
  getProductCountForUser(1); // Direct admin cashier
  getProductCountForUser(2); // Branch A cashier
  getProductCountForUser(3); // Branch B cashier
  getProductCountForUser(4); // Branch C cashier

  console.log('\n\n2. CATEGORY COUNT TESTS');
  testCategoryCount(1); // Direct admin cashier
  testCategoryCount(2); // Branch A cashier
  testCategoryCount(3); // Branch B cashier
  testCategoryCount(4); // Branch C cashier

  console.log('\n\n3. API PARAMETER SIMULATION');
  simulateAPICall(1); // Direct admin cashier
  simulateAPICall(2); // Branch A cashier
  simulateAPICall(4); // Branch C cashier

  console.log('\n\n4. BEFORE/AFTER COMPARISON');
  testBeforeAfterComparison();

  console.log('\n=== Test Complete ===');
  console.log('\n📋 Expected Results:');
  console.log('✅ "All Menu" count matches actual accessible products');
  console.log('✅ Category counts match products in each category');
  console.log('✅ Branch cashiers see only their branch product counts');
  console.log('✅ Admin cashiers see only admin product counts');
  console.log('✅ Inactive products are not counted');
  console.log('✅ Complete consistency between products and category counts');
};

// Run the test
testCategoryCountFix();
