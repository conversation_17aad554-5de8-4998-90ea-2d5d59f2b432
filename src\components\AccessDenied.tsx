import React, { useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { ShieldX, ArrowLeft, Home, LogIn } from 'lucide-react';

interface AccessDeniedProps {
  userRole?: string;
  requiredRole?: string;
  message?: string;
}

const AccessDenied: React.FC<AccessDeniedProps> = ({
  userRole = 'unknown',
  requiredRole = 'kasir',
  message
}) => {
  const navigate = useNavigate();

  // Jika role unknown, redirect ke login setelah 3 detik
  useEffect(() => {
    if (userRole === 'unknown' || !userRole) {
      const timer = setTimeout(() => {
        navigate('/login', { replace: true });
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [userRole, navigate]);

  // Jika role unknown, tampilkan pesan khusus
  if (userRole === 'unknown' || !userRole) {
    return (
      <div className="min-h-screen bg-neutral-50 flex items-center justify-center px-4">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
          {/* Icon */}
          <div className="mx-auto w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mb-6">
            <LogIn size={32} className="text-yellow-600" />
          </div>

          {/* Title */}
          <h1 className="text-2xl font-bold text-neutral-900 mb-4">
            Sesi Tidak Valid
          </h1>

          {/* Message */}
          <p className="text-neutral-600 mb-6 leading-relaxed">
            Role pengguna tidak dikenali. Anda akan diarahkan ke halaman login dalam 3 detik...
          </p>

          {/* Loading indicator */}
          <div className="flex justify-center mb-6">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
          </div>

          {/* Action Button */}
          <Link
            to="/login"
            className="w-full bg-primary-600 text-white py-3 px-4 rounded-lg hover:bg-primary-700 transition-colors flex items-center justify-center gap-2"
          >
            <LogIn size={18} />
            Login Sekarang
          </Link>
        </div>
      </div>
    );
  }

  const defaultMessage = `Halaman ini hanya dapat diakses oleh ${requiredRole}. Role Anda saat ini: ${userRole}`;

  return (
    <div className="min-h-screen bg-neutral-50 flex items-center justify-center px-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
        {/* Icon */}
        <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-6">
          <ShieldX size={32} className="text-red-600" />
        </div>

        {/* Title */}
        <h1 className="text-2xl font-bold text-neutral-900 mb-4">
          Akses Ditolak
        </h1>

        {/* Message */}
        <p className="text-neutral-600 mb-8 leading-relaxed">
          {message || defaultMessage}
        </p>

        {/* Action Buttons */}
        <div className="space-y-3">
          <Link
            to="/transactions"
            className="w-full bg-primary-600 text-white py-3 px-4 rounded-lg hover:bg-primary-700 transition-colors flex items-center justify-center gap-2"
          >
            <Home size={18} />
            Ke Halaman Transaksi
          </Link>

          <button
            onClick={() => window.history.back()}
            className="w-full bg-neutral-200 text-neutral-700 py-3 px-4 rounded-lg hover:bg-neutral-300 transition-colors flex items-center justify-center gap-2"
          >
            <ArrowLeft size={18} />
            Kembali
          </button>
        </div>

        {/* Additional Info */}
        <div className="mt-8 p-4 bg-neutral-50 rounded-lg">
          <p className="text-sm text-neutral-500">
            Jika Anda merasa ini adalah kesalahan, silakan hubungi administrator sistem.
          </p>
        </div>
      </div>
    </div>
  );
};

export default AccessDenied;
