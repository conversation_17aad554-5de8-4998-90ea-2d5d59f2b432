const express = require('express');
const router = express.Router();
const PaymentGateway = require('../models/PaymentGateway');
const pool = require('../config/database');

// Get all payment gateways
router.get('/', async (req, res) => {
  try {
    const { user_id } = req.query;

    let query = `SELECT * FROM ${PaymentGateway.$table}`;
    const params = [];

    // Filter berdasarkan user_id jika ada
    if (user_id) {
      query += ' WHERE user_id = ?';
      params.push(user_id);
    }

    const [rows] = await pool.query(query, params);
    res.json(rows);
  } catch (error) {
    console.error('Error fetching payment gateways:', error);
    res.status(500).json({ error: 'Failed to fetch payment gateways' });
  }
});

// Get payment gateway by ID
router.get('/:id', async (req, res) => {
  try {
    const { user_id } = req.query;

    let query = `SELECT * FROM ${PaymentGateway.$table} WHERE id = ?`;
    const params = [req.params.id];

    // Filter berdasarkan user_id jika ada
    if (user_id) {
      query += ' AND user_id = ?';
      params.push(user_id);
    }

    const [rows] = await pool.query(query, params);

    if (rows.length === 0) {
      return res.status(404).json({ error: 'Payment gateway not found' });
    }

    res.json(rows[0]);
  } catch (error) {
    console.error('Error fetching payment gateway:', error);
    res.status(500).json({ error: 'Failed to fetch payment gateway' });
  }
});

// Create or update payment gateway
router.post('/', async (req, res) => {
  try {
    const { provider, is_production, client_key, server_key, is_active, user_id } = req.body;

    // Validasi input
    if (!provider || !user_id) {
      return res.status(400).json({ error: 'Provider name and user_id are required' });
    }

    // Cek apakah provider sudah ada untuk user_id yang sama
    const [existingRows] = await pool.query(
      `SELECT * FROM ${PaymentGateway.$table} WHERE provider = ? AND user_id = ?`,
      [provider, user_id]
    );

    const existingGateway = existingRows[0];

    if (existingGateway) {
      // Jika provider sudah ada, update gateway yang ada
      console.log(`Provider ${provider} already exists for user ${user_id}, updating instead of creating`);

      const [updateResult] = await pool.query(
        `UPDATE ${PaymentGateway.$table} SET 
         is_production = ?, 
         client_key = ?, 
         server_key = ?, 
         is_active = ? 
         WHERE id = ?`,
        [
          is_production ? 1 : 0,
          client_key || '',
          server_key || '',
          is_active ? 1 : 0,
          existingGateway.id
        ]
      );

      if (updateResult.affectedRows > 0) {
        const [updatedRows] = await pool.query(
          `SELECT * FROM ${PaymentGateway.$table} WHERE id = ?`,
          [existingGateway.id]
        );
        return res.json(updatedRows[0]);
      } else {
        return res.status(500).json({ error: 'Failed to update existing payment gateway' });
      }
    }

    // Jika provider belum ada, buat gateway baru
    const [insertResult] = await pool.query(
      `INSERT INTO ${PaymentGateway.$table} 
       (provider, is_production, client_key, server_key, is_active, user_id) 
       VALUES (?, ?, ?, ?, ?, ?)`,
      [
        provider,
        is_production ? 1 : 0,
        client_key || '',
        server_key || '',
        is_active ? 1 : 0,
        user_id
      ]
    );

    const newGateway = {
      id: insertResult.insertId,
      provider,
      is_production: is_production ? 1 : 0,
      client_key: client_key || '',
      server_key: server_key || '',
      is_active: is_active ? 1 : 0,
      user_id
    };

    res.status(201).json(newGateway);
  } catch (error) {
    console.error('Error creating/updating payment gateway:', error);
    res.status(500).json({ error: 'Failed to create/update payment gateway' });
  }
});

// Update payment gateway
router.put('/:id', async (req, res) => {
  try {
    const { provider, is_production, client_key, server_key, is_active, user_id } = req.body;

    // Validasi input
    if (!provider || !user_id) {
      return res.status(400).json({ error: 'Provider name and user_id are required' });
    }

    // Cek apakah gateway ada dan milik user yang sama
    const [existingRows] = await pool.query(
      `SELECT * FROM ${PaymentGateway.$table} WHERE id = ? AND user_id = ?`,
      [req.params.id, user_id]
    );

    if (existingRows.length === 0) {
      return res.status(404).json({ error: 'Payment gateway not found or unauthorized' });
    }

    const existingGateway = existingRows[0];

    // Cek apakah provider sudah digunakan oleh gateway lain milik user yang sama
    if (provider !== existingGateway.provider) {
      const [existingProviders] = await pool.query(
        `SELECT * FROM ${PaymentGateway.$table} WHERE provider = ? AND id != ? AND user_id = ?`,
        [provider, req.params.id, user_id]
      );

      if (existingProviders.length > 0) {
        return res.status(400).json({ error: 'Payment gateway provider already exists' });
      }
    }

    const [updateResult] = await pool.query(
      `UPDATE ${PaymentGateway.$table} SET 
       provider = ?,
       is_production = ?, 
       client_key = ?, 
       server_key = ?, 
       is_active = ? 
       WHERE id = ?`,
      [
        provider,
        is_production ? 1 : 0,
        client_key || '',
        server_key || '',
        is_active ? 1 : 0,
        req.params.id
      ]
    );

    if (updateResult.affectedRows > 0) {
      const [updatedRows] = await pool.query(
        `SELECT * FROM ${PaymentGateway.$table} WHERE id = ?`,
        [req.params.id]
      );
      res.json(updatedRows[0]);
    } else {
      res.status(404).json({ error: 'Payment gateway not found' });
    }
  } catch (error) {
    console.error('Error updating payment gateway:', error);
    res.status(500).json({ error: 'Failed to update payment gateway' });
  }
});

// Delete payment gateway
router.delete('/:id', async (req, res) => {
  try {
    const { user_id } = req.query;

    if (!user_id) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Cek apakah gateway ada dan milik user yang sama
    const [existingRows] = await pool.query(
      `SELECT * FROM ${PaymentGateway.$table} WHERE id = ? AND user_id = ?`,
      [req.params.id, user_id]
    );

    if (existingRows.length === 0) {
      return res.status(404).json({ error: 'Payment gateway not found or unauthorized' });
    }

    const [deleteResult] = await pool.query(
      `DELETE FROM ${PaymentGateway.$table} WHERE id = ?`,
      [req.params.id]
    );

    if (deleteResult.affectedRows > 0) {
      res.json({ message: 'Payment gateway deleted successfully' });
    } else {
      res.status(500).json({ error: 'Failed to delete payment gateway' });
    }
  } catch (error) {
    console.error('Error deleting payment gateway:', error);
    res.status(500).json({ error: 'Failed to delete payment gateway' });
  }
});

module.exports = router;




