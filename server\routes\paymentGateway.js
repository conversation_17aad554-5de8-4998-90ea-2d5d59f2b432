const express = require('express');
const router = express.Router();
const pool = require('../config/database');
const PaymentGateway = require('../models/PaymentGateway');
const axios = require('axios');
const crypto = require('crypto');

// Endpoint untuk memproses pembayaran melalui gateway
router.post('/process', async (req, res) => {
  try {
    const { transaction_id, amount, payment_method_id, payment_type, user_id, branch_id, cashier_id } = req.body;

    // Validasi input
    if (!transaction_id || !amount || !payment_method_id || !user_id) {
      return res.status(400).json({ error: 'Semua field diperlukan' });
    }

    // Ambil data transaksi
    const [transactions] = await pool.query(
      'SELECT * FROM transactions WHERE id = ?',
      [transaction_id]
    );

    if (transactions.length === 0) {
      return res.status(404).json({ error: 'Transaksi tidak ditemukan' });
    }

    const transaction = transactions[0];

    // Tentukan user_id yang akan digunakan untuk mengambil konfigurasi
    let configUserId = user_id;
    let actualBranchId = branch_id;

    // Jika request dari kasir, tentukan branch_id dan user_id admin
    if (cashier_id) {
      const [cashierResult] = await pool.query(
        'SELECT branch_id FROM cashiers WHERE id = ?',
        [cashier_id]
      );

      if (cashierResult.length > 0) {
        actualBranchId = cashierResult[0].branch_id;

        // Jika kasir memiliki branch_id, ambil user_id admin dari branch
        if (actualBranchId) {
          const [branchResult] = await pool.query(
            'SELECT user_id FROM branches WHERE id = ?',
            [actualBranchId]
          );

          if (branchResult.length > 0) {
            configUserId = branchResult[0].user_id;
          }
        }
      }
    } else if (branch_id) {
      // Jika request dari cabang, cek apakah perlu menggunakan konfigurasi dari admin
      // Dapatkan user_id (admin/parent) dari cabang
      const [branchResult] = await pool.query(
        'SELECT user_id FROM branches WHERE id = ?',
        [branch_id]
      );

      if (branchResult.length > 0) {
        // Gunakan user_id admin untuk mengambil konfigurasi
        configUserId = branchResult[0].user_id;
      }
    }

    // Ambil data metode pembayaran
    // Jika actualBranchId ada, cek metode pembayaran milik admin atau cabang
    let paymentMethodQuery = 'SELECT * FROM payment_methods WHERE id = ?';
    const paymentMethodParams = [payment_method_id];

    if (actualBranchId) {
      // Dapatkan user_id admin
      const [branchResult] = await pool.query(
        'SELECT user_id FROM branches WHERE id = ?',
        [actualBranchId]
      );

      if (branchResult.length > 0) {
        const adminId = branchResult[0].user_id;
        paymentMethodQuery += ' AND (user_id = ? OR user_id = ?)';
        paymentMethodParams.push(adminId, configUserId);
      } else {
        paymentMethodQuery += ' AND user_id = ?';
        paymentMethodParams.push(configUserId);
      }
    } else {
      paymentMethodQuery += ' AND user_id = ?';
      paymentMethodParams.push(configUserId);
    }

    const [paymentMethods] = await pool.query(paymentMethodQuery, paymentMethodParams);

    if (paymentMethods.length === 0) {
      return res.status(404).json({ error: 'Metode pembayaran tidak ditemukan' });
    }

    const paymentMethod = paymentMethods[0];

    // Validasi bahwa metode pembayaran mendukung gateway
    if (!paymentMethod.use_gateway) {
      return res.status(400).json({
        error: 'Metode pembayaran tidak dikonfigurasi untuk menggunakan payment gateway',
        message: 'Aktifkan opsi "Gunakan Payment Gateway" pada metode pembayaran ini'
      });
    }

    // Validasi tipe pembayaran yang didukung
    if (paymentMethod.type !== 'qris' && paymentMethod.type !== 'e_wallet') {
      return res.status(400).json({
        error: 'Tipe pembayaran tidak didukung untuk payment gateway',
        message: 'Hanya metode pembayaran QRIS dan e-wallet yang didukung untuk payment gateway'
      });
    }

    // Ambil konfigurasi Midtrans dari admin jika cabang
    const midtransConfig = await PaymentGateway.getByProvider('midtrans', configUserId);

    if (!midtransConfig) {
      return res.status(404).json({ error: 'Konfigurasi Midtrans tidak ditemukan' });
    }

    if (!midtransConfig.server_key || midtransConfig.server_key.trim() === '') {
      return res.status(400).json({
        error: 'Server key Midtrans tidak valid',
        message: 'Pastikan konfigurasi Midtrans sudah diatur dengan benar di pengaturan payment gateway. Server key tidak boleh kosong.'
      });
    }

    // Server key validation removed - allow any server key format

    if (!midtransConfig.is_active) {
      return res.status(400).json({
        error: 'Konfigurasi Midtrans tidak aktif',
        message: 'Aktifkan konfigurasi Midtrans di pengaturan payment gateway'
      });
    }

    // Tentukan URL API Midtrans berdasarkan mode (production/sandbox)
    const baseUrl = midtransConfig.is_production
      ? 'https://api.midtrans.com'
      : 'https://api.sandbox.midtrans.com';

    // Buat ID unik untuk transaksi Midtrans
    const midtransTransactionId = `${transaction_id}-${Date.now()}`;

    // Siapkan data untuk request ke Midtrans
    let requestData;
    let endpoint;

    // Tentukan jenis pembayaran dan endpoint yang sesuai
    if (paymentMethod.type === 'qris') {
      // Gunakan Core API untuk QRIS
      endpoint = `${baseUrl}/v2/charge`;
      requestData = {
        payment_type: 'qris',
        transaction_details: {
          order_id: midtransTransactionId,
          gross_amount: parseInt(amount)
        },
        qris: {
          acquirer: 'gopay'
        },
        // Add customer details for better transaction tracking
        customer_details: {
          first_name: 'Customer',
          email: '<EMAIL>'
        }
      };
    } else if (paymentMethod.type === 'e_wallet') {
      // Support for e-wallet payments
      endpoint = `${baseUrl}/v2/charge`;
      requestData = {
        payment_type: 'gopay',
        transaction_details: {
          order_id: midtransTransactionId,
          gross_amount: parseInt(amount)
        },
        gopay: {
          enable_callback: true,
          callback_url: `${req.protocol}://${req.get('host')}/api/payment-gateway/callback`
        }
      };
    } else {
      return res.status(400).json({ error: 'Tipe pembayaran tidak didukung. Hanya QRIS dan e-wallet yang didukung.' });
    }

    // Debug logging
    console.log('Sending request to Midtrans:', {
      endpoint,
      requestData,
      serverKey: midtransConfig.server_key ? 'SET' : 'NOT_SET',
      isProduction: midtransConfig.is_production
    });

    // Kirim request ke Midtrans
    try {
      const response = await axios.post(endpoint, requestData, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': `Basic ${Buffer.from(midtransConfig.server_key + ':').toString('base64')}`
        }
      });

      // Simpan data transaksi gateway ke database
      const [result] = await pool.query(
        `INSERT INTO payment_gateway_transactions 
         (transaction_id, gateway_transaction_id, gateway_provider, amount, payment_type, status, response_data) 
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [
          transaction_id,
          midtransTransactionId,
          'midtrans',
          amount,
          paymentMethod.type,
          'pending',
          JSON.stringify(response.data)
        ]
      );

      // Persiapkan data respons
      let responseData;

      if (paymentMethod.type === 'qris') {
        // Extract QR code URL from Midtrans response
        let qrCodeUrl = null;

        // Try different possible fields for QR code URL
        if (response.data.qr_string) {
          qrCodeUrl = response.data.qr_string;
        } else if (response.data.qr_code_url) {
          qrCodeUrl = response.data.qr_code_url;
        } else if (response.data.actions) {
          const qrAction = response.data.actions.find(a => a.name === 'generate-qr-code');
          if (qrAction && qrAction.url) {
            qrCodeUrl = qrAction.url;
          }
        }

        // Log the response for debugging
        console.log('Midtrans QRIS response:', JSON.stringify(response.data, null, 2));
        console.log('Extracted QR code URL:', qrCodeUrl);

        if (!qrCodeUrl) {
          return res.status(500).json({
            error: 'QR code tidak tersedia',
            message: 'Midtrans tidak mengembalikan QR code URL. Periksa konfigurasi QRIS.',
            debug: response.data
          });
        }

        responseData = {
          transaction_id: midtransTransactionId,
          qr_code_url: qrCodeUrl,
          expiry_time: new Date(Date.now() + 10 * 60 * 1000).toISOString() // 10 menit dari sekarang
        };
      }

      // Kirim respons ke client
      res.json(responseData);
    } catch (error) {
      console.error('Error from Midtrans API:', error.response?.data || error.message);

      // Tangani error dari Midtrans
      if (error.response && error.response.data) {
        const errorData = error.response.data;
        let errorMessage = 'Pastikan konfigurasi Midtrans (server key) sudah benar dan akun Midtrans Anda aktif';

        // Provide more specific error messages based on Midtrans error codes
        if (errorData.error_messages) {
          errorMessage = errorData.error_messages.join(', ');
        } else if (errorData.status_message) {
          errorMessage = errorData.status_message;
        }

        return res.status(error.response.status || 500).json({
          error: 'Gagal memproses pembayaran melalui gateway',
          details: errorData,
          message: errorMessage
        });
      }

      res.status(500).json({
        error: 'Gagal memproses pembayaran melalui gateway',
        message: error.message || 'Terjadi kesalahan pada server'
      });
    }
  } catch (error) {
    console.error('Error processing payment gateway:', error);

    // Tangani error dari Midtrans
    if (error.response && error.response.data) {
      return res.status(error.response.status).json({
        error: 'Gagal memproses pembayaran melalui gateway',
        details: error.response.data
      });
    }

    res.status(500).json({ error: 'Gagal memproses pembayaran melalui gateway' });
  }
});

// Endpoint untuk mengecek status transaksi dari Midtrans
router.get('/status/:orderId', async (req, res) => {
  try {
    const { orderId } = req.params;

    // Ambil data transaksi gateway dari database untuk mendapatkan konfigurasi
    const [gatewayTransactions] = await pool.query(
      'SELECT * FROM payment_gateway_transactions WHERE gateway_transaction_id = ?',
      [orderId]
    );

    if (gatewayTransactions.length === 0) {
      return res.status(404).json({ error: 'Transaksi gateway tidak ditemukan' });
    }

    const gatewayTransaction = gatewayTransactions[0];

    // Ambil transaksi utama untuk mendapatkan user_id
    const [transactions] = await pool.query(
      'SELECT * FROM transactions WHERE id = ?',
      [gatewayTransaction.transaction_id]
    );

    if (transactions.length === 0) {
      return res.status(404).json({ error: 'Transaksi tidak ditemukan' });
    }

    const transaction = transactions[0];

    // Tentukan user_id untuk konfigurasi Midtrans
    let configUserId = transaction.outlet_id;
    if (transaction.branch_id) {
      const [branches] = await pool.query('SELECT user_id FROM branches WHERE id = ?', [transaction.branch_id]);
      if (branches.length > 0) {
        configUserId = branches[0].user_id;
      }
    }
    if (transaction.cashier_id) {
      const [cashiers] = await pool.query('SELECT user_id, branch_id FROM cashiers WHERE id = ?', [transaction.cashier_id]);
      if (cashiers.length > 0) {
        if (cashiers[0].branch_id) {
          const [branches] = await pool.query('SELECT user_id FROM branches WHERE id = ?', [cashiers[0].branch_id]);
          if (branches.length > 0) {
            configUserId = branches[0].user_id;
          }
        } else {
          configUserId = cashiers[0].user_id;
        }
      }
    }

    // Ambil konfigurasi Midtrans
    const midtransConfig = await PaymentGateway.getByProvider('midtrans', configUserId);

    if (!midtransConfig) {
      return res.status(404).json({ error: 'Konfigurasi Midtrans tidak ditemukan' });
    }

    // Tentukan URL API Midtrans berdasarkan mode (production/sandbox)
    const baseUrl = midtransConfig.is_production
      ? 'https://api.midtrans.com'
      : 'https://api.sandbox.midtrans.com';

    // Panggil Midtrans API untuk mengecek status
    const statusUrl = `${baseUrl}/v2/${orderId}/status`;

    const response = await axios.get(statusUrl, {
      headers: {
        'Accept': 'application/json',
        'Authorization': `Basic ${Buffer.from(midtransConfig.server_key + ':').toString('base64')}`
      }
    });

    // Update status di database jika diperlukan
    const midtransStatus = response.data.transaction_status;
    let dbStatus = gatewayTransaction.status;

    if (midtransStatus === 'settlement' || midtransStatus === 'capture') {
      dbStatus = 'paid';
    } else if (midtransStatus === 'failure' || midtransStatus === 'cancel' || midtransStatus === 'expire') {
      dbStatus = 'cancelled';
    } else if (midtransStatus === 'pending') {
      dbStatus = 'pending';
    }

    // Update status di payment_gateway_transactions
    await pool.query(
      'UPDATE payment_gateway_transactions SET status = ? WHERE gateway_transaction_id = ?',
      [dbStatus, orderId]
    );

    // Return status dari Midtrans
    res.json({
      transaction_status: midtransStatus,
      order_id: response.data.order_id,
      gross_amount: response.data.gross_amount,
      payment_type: response.data.payment_type,
      transaction_time: response.data.transaction_time,
      fraud_status: response.data.fraud_status
    });

  } catch (error) {
    console.error('Error checking transaction status:', error);

    if (error.response && error.response.data) {
      return res.status(error.response.status || 500).json({
        error: 'Gagal mengecek status transaksi',
        details: error.response.data
      });
    }

    res.status(500).json({ error: 'Gagal mengecek status transaksi' });
  }
});

// Endpoint untuk menerima callback dari Midtrans
router.post('/callback', async (req, res) => {
  try {
    const notification = req.body;
    console.log('Midtrans callback received:', notification);

    const orderId = notification.order_id;
    const transactionStatus = notification.transaction_status;
    const fraudStatus = notification.fraud_status;

    // Update status transaksi di database berdasarkan callback
    if (transactionStatus === 'capture') {
      if (fraudStatus === 'challenge') {
        // TODO: Set transaction status on your database to 'challenge'
        console.log('Transaction challenge:', orderId);
      } else if (fraudStatus === 'accept') {
        // TODO: Set transaction status on your database to 'success'
        console.log('Transaction success:', orderId);
      }
    } else if (transactionStatus === 'settlement') {
      // TODO: Set transaction status on your database to 'success'
      console.log('Transaction settlement:', orderId);
    } else if (transactionStatus === 'cancel' || transactionStatus === 'deny' || transactionStatus === 'expire') {
      // TODO: Set transaction status on your database to 'failure'
      console.log('Transaction failed:', orderId, transactionStatus);
    } else if (transactionStatus === 'pending') {
      // TODO: Set transaction status on your database to 'pending'
      console.log('Transaction pending:', orderId);
    }

    res.status(200).json({ status: 'OK' });
  } catch (error) {
    console.error('Error processing Midtrans callback:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;









