import React from 'react';
import MenuCard from './MenuCard';
import { MenuItem } from '../types';
import { Coffee, UtensilsCrossed } from 'lucide-react';

interface MenuSectionProps {
  title: string;
  items: MenuItem[];
}

const MenuSection: React.FC<MenuSectionProps> = ({ title, items }) => {
  return (
    <div className="mb-10 animate-fade-in">
      <h2 className="section-heading">
        {title === 'Makanan' ? (
          <UtensilsCrossed size={24} className="text-primary-600" />
        ) : (
          <Coffee size={24} className="text-primary-600" />
        )}
        {title}
      </h2>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-6">
        {items.map((item, index) => (
          <div
            key={item.id}
            className="animate-slide-up"
            style={{ animationDelay: `${index * 0.05}s` }}
          >
            <MenuCard item={item} />
          </div>
        ))}
      </div>
    </div>
  );
};

export default MenuSection;