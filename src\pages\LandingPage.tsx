import { useState } from 'react';
import { Link } from 'react-router-dom';
import { Coffee, ShoppingBag, BarChart2, Users, ChevronRight, Check } from 'lucide-react';

const LandingPage = () => {
  const [activeTab, setActiveTab] = useState('umkm');

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <header className="bg-gradient-to-r from-primary-700 to-primary-800 text-white">
        <div className="container mx-auto px-4 py-16 md:py-24">
          <div className="flex flex-col md:flex-row items-center">
            <div className="md:w-1/2 mb-10 md:mb-0">
              <div className="inline-block mb-4">
                <img src="/satulisan.png" alt="" width={"100px"} />
              </div>
              <h1 className="text-4xl md:text-5xl font-bold mb-4 leading-tight text-white">
                <PERSON><PERSON> - <PERSON>man Setia UMKM
              </h1>
              <p className="text-xl mb-8 text-primary-100">
                <PERSON><PERSON><PERSON> kasir digital lengkap untuk membantu bisnis Anda tumbuh lebih cepat
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  to="/login"
                  className="btn bg-accent-500 hover:bg-accent-600 text-white px-6 py-3 rounded-md font-medium transition-all duration-300 text-center"
                >
                  Mulai Sekarang
                </Link>
                <a
                  href="#features"
                  className="btn bg-transparent border border-white text-white px-6 py-3 rounded-md font-medium transition-all duration-300 text-center"
                >
                  Pelajari Fitur
                </a>
              </div>
            </div>
            <div className="md:w-1/2 flex justify-center">
              <div className="relative">
                <div className="bg-white rounded-xl shadow-xl overflow-hidden">
                  <img
                    src="/dashboard-preview.png"
                    alt="Satu Lisan POS Dashboard"
                    className="w-full h-auto"
                    onError={(e) => {
                      e.currentTarget.src = 'https://via.placeholder.com/600x400?text=Satu Lisan POS+Dashboard';
                    }}
                  />
                </div>
                <div className="absolute -bottom-4 -right-4 bg-accent-500 text-white px-4 py-2 rounded-lg shadow-lg">
                  Mudah Digunakan!
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Features Section */}
      <section id="features" className="py-16 bg-neutral-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-primary-800 mb-4">Fitur Unggulan</h2>
            <p className="text-neutral-600 max-w-2xl mx-auto">
              Satu Lisan hadir dengan berbagai fitur yang dirancang khusus untuk memenuhi kebutuhan UMKM Indonesia
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all">
              <div className="bg-primary-100 p-3 rounded-full inline-block mb-4">
                <ShoppingBag className="h-6 w-6 text-primary-600" />
              </div>
              <h3 className="text-xl font-semibold text-primary-800 mb-2">Kasir Digital</h3>
              <p className="text-neutral-600">
                Proses transaksi dengan cepat dan mudah. Mendukung berbagai metode pembayaran.
              </p>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all">
              <div className="bg-primary-100 p-3 rounded-full inline-block mb-4">
                <BarChart2 className="h-6 w-6 text-primary-600" />
              </div>
              <h3 className="text-xl font-semibold text-primary-800 mb-2">Laporan Bisnis</h3>
              <p className="text-neutral-600">
                Pantau perkembangan bisnis Anda dengan laporan penjualan yang detail dan mudah dipahami.
              </p>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all">
              <div className="bg-primary-100 p-3 rounded-full inline-block mb-4">
                <Users className="h-6 w-6 text-primary-600" />
              </div>
              <h3 className="text-xl font-semibold text-primary-800 mb-2">Manajemen Member</h3>
              <p className="text-neutral-600">
                Kelola data pelanggan dan tingkatkan loyalitas dengan program member.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-primary-800 mb-4">Untuk Siapa Satu Lisan?</h2>
            <div className="flex justify-center space-x-4 mb-8">
              <button
                className={`px-4 py-2 rounded-md ${activeTab === 'umkm'
                  ? 'bg-primary-600 text-white'
                  : 'bg-neutral-100 text-neutral-700 hover:bg-neutral-200'
                  }`}
                onClick={() => setActiveTab('umkm')}
              >
                UMKM
              </button>
              <button
                className={`px-4 py-2 rounded-md ${activeTab === 'resto'
                  ? 'bg-primary-600 text-white'
                  : 'bg-neutral-100 text-neutral-700 hover:bg-neutral-200'
                  }`}
                onClick={() => setActiveTab('resto')}
              >
                Restoran & Kafe
              </button>
              <button
                className={`px-4 py-2 rounded-md ${activeTab === 'retail'
                  ? 'bg-primary-600 text-white'
                  : 'bg-neutral-100 text-neutral-700 hover:bg-neutral-200'
                  }`}
                onClick={() => setActiveTab('retail')}
              >
                Retail
              </button>
            </div>
          </div>

          <div className="bg-neutral-50 rounded-xl p-8">
            {activeTab === 'umkm' && (
              <div className="animate-fade-in">
                <h3 className="text-xl font-semibold text-primary-800 mb-4">Solusi untuk UMKM</h3>
                <ul className="space-y-3">
                  <li className="flex items-start">
                    <Check className="h-5 w-5 text-primary-600 mr-2 mt-0.5" />
                    <span>Mudah digunakan tanpa perlu keahlian teknis</span>
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 text-primary-600 mr-2 mt-0.5" />
                    <span>Harga terjangkau dengan fitur lengkap</span>
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 text-primary-600 mr-2 mt-0.5" />
                    <span>Laporan keuangan sederhana untuk memantau pertumbuhan bisnis</span>
                  </li>
                </ul>
              </div>
            )}

            {activeTab === 'resto' && (
              <div className="animate-fade-in">
                <h3 className="text-xl font-semibold text-primary-800 mb-4">Solusi untuk Restoran & Kafe</h3>
                <ul className="space-y-3">
                  <li className="flex items-start">
                    <Check className="h-5 w-5 text-primary-600 mr-2 mt-0.5" />
                    <span>Manajemen menu dan kategori yang fleksibel</span>
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 text-primary-600 mr-2 mt-0.5" />
                    <span>Proses pemesanan yang cepat dan efisien</span>
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 text-primary-600 mr-2 mt-0.5" />
                    <span>Integrasi dengan printer untuk struk pembayaran</span>
                  </li>
                </ul>
              </div>
            )}

            {activeTab === 'retail' && (
              <div className="animate-fade-in">
                <h3 className="text-xl font-semibold text-primary-800 mb-4">Solusi untuk Retail</h3>
                <ul className="space-y-3">
                  <li className="flex items-start">
                    <Check className="h-5 w-5 text-primary-600 mr-2 mt-0.5" />
                    <span>Manajemen inventori yang komprehensif</span>
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 text-primary-600 mr-2 mt-0.5" />
                    <span>Dukungan untuk barcode dan scanner</span>
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 text-primary-600 mr-2 mt-0.5" />
                    <span>Laporan penjualan dan analitik untuk optimasi stok</span>
                  </li>
                </ul>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-primary-700 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6 text-white">Siap Memulai dengan Satu Lisan?</h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto text-primary-100">
            Bergabunglah dengan ribuan UMKM yang telah menggunakan Satu Lisan untuk mengembangkan bisnis mereka
          </p>
          <Link
            to="/login"
            className="inline-flex items-center bg-accent-500 hover:bg-accent-600 text-white px-6 py-3 rounded-md font-medium transition-all duration-300"
          >
            Mulai Gratis 14 Hari
            <ChevronRight className="ml-2 h-5 w-5" />
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-neutral-900 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between">
            <div className="mb-8 md:mb-0">
              <div className="flex items-center mb-4">
                <div className="bg-primary-600 p-2 rounded-full mr-2">
                  <Coffee className="h-6 w-6 text-white" />
                </div>
                <span className="text-xl font-bold">Satu Lisan</span>
              </div>
              <p className="text-neutral-400 max-w-md">
                Satu Lisan adalah solusi kasir digital terpercaya untuk UMKM Indonesia.
                Kami berkomitmen membantu bisnis Anda tumbuh dengan teknologi yang mudah digunakan.
              </p>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-8">
              <div>
                <h3 className="text-lg font-semibold mb-4">Produk</h3>
                <ul className="space-y-2">
                  <li><a href="#" className="text-neutral-400 hover:text-white">Fitur</a></li>
                  <li><a href="#" className="text-neutral-400 hover:text-white">Harga</a></li>
                  <li><a href="#" className="text-neutral-400 hover:text-white">Integrasi</a></li>
                </ul>
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-4">Perusahaan</h3>
                <ul className="space-y-2">
                  <li><a href="#" className="text-neutral-400 hover:text-white">Tentang Kami</a></li>
                  <li><a href="#" className="text-neutral-400 hover:text-white">Blog</a></li>
                  <li><a href="#" className="text-neutral-400 hover:text-white">Karir</a></li>
                </ul>
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-4">Bantuan</h3>
                <ul className="space-y-2">
                  <li><a href="#" className="text-neutral-400 hover:text-white">Kontak</a></li>
                  <li><a href="#" className="text-neutral-400 hover:text-white">Dokumentasi</a></li>
                  <li><a href="#" className="text-neutral-400 hover:text-white">FAQ</a></li>
                </ul>
              </div>
            </div>
          </div>
          <div className="border-t border-neutral-800 mt-12 pt-8 text-center text-neutral-500">
            <p>© {new Date().getFullYear()} Satu Lisan. Hak Cipta Dilindungi.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default LandingPage;

