import React, { useState, useEffect } from 'react';
import { BarChart3, Users, ShoppingCart, DollarSign, Clock, TrendingUp } from 'lucide-react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Bar, Line } from 'react-chartjs-2';
import config from '../config';
import { getCurrentUser } from '../utils/api';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend
);

interface DashboardSummaryProps {
  userRole: 'admin' | 'branch';
  userId: number;
  branchId?: number;
}

interface SummaryData {
  totalSales: number;
  totalTransactions: number;
  totalCustomers: number;
  totalProducts: number;
  todaySales: number;
  todayTransactions: number;
  monthSales: number;
  monthTransactions: number;
  topProducts: Array<{
    id: number;
    name: string;
    sales: number;
    quantity: number;
  }>;
  recentTransactions: Array<{
    id: string;
    customer_name: string;
    total: number;
    date: string;
    payment_method: string;
  }>;
}

interface Branch {
  id: number;
  name: string;
}

interface TimeSlotData {
  time_slot: string;
  transaction_count: number;
  total_sales: number;
}

interface RevenueExpenseData {
  date: string;
  revenue: number;
  expenses: number;
  profit: number;
}

const DashboardSummary: React.FC<DashboardSummaryProps> = ({ userRole, userId, branchId }) => {
  const [summaryData, setSummaryData] = useState<SummaryData | null>(null);
  const [branches, setBranches] = useState<Branch[]>([]);
  const [selectedBranch, setSelectedBranch] = useState<number | 'all'>(branchId || 'all');
  const [loading, setLoading] = useState(true);
  const [dateFilter, setDateFilter] = useState('today');
  const [timeSlotData, setTimeSlotData] = useState<TimeSlotData[]>([]);
  const [revenueExpenseData, setRevenueExpenseData] = useState<RevenueExpenseData[]>([]);
  const [chartsLoading, setChartsLoading] = useState(true);

  // Fetch branches untuk admin
  useEffect(() => {
    if (userRole === 'admin') {
      fetchBranches();
    }
  }, [userRole]);

  // Fetch summary data
  useEffect(() => {
    fetchSummaryData();
    fetchChartData();
  }, [selectedBranch, dateFilter, userId, branchId]);

  const fetchBranches = async () => {
    try {
      const response = await fetch(`${config.apiUrl}/branches?user_id=${userId}`);
      if (response.ok) {
        const data = await response.json();
        setBranches(data);
      }
    } catch (error) {
      console.error('Error fetching branches:', error);
    }
  };

  const fetchSummaryData = async () => {
    setLoading(true);
    try {
      const currentUser = getCurrentUser();
      const params = new URLSearchParams({
        user_id: userId.toString(),
        date_filter: dateFilter,
        user_type: currentUser?.user_type || userRole, // Use user_type from session for precise table identification
      });

      // Untuk admin, bisa filter berdasarkan cabang
      if (userRole === 'admin' && selectedBranch !== 'all') {
        params.append('branch_id', selectedBranch.toString());
      }
      // Untuk branch, selalu gunakan branch_id mereka
      else if (userRole === 'branch' && branchId) {
        params.append('branch_id', branchId.toString());
      }

      console.log('Fetching dashboard data with params:', params.toString());
      const response = await fetch(`${config.apiUrl}/dashboard/summary?${params}`);

      if (response.ok) {
        const data = await response.json();
        console.log('Dashboard data received:', data);
        setSummaryData(data);
      } else {
        console.error('Failed to fetch dashboard data:', response.status, response.statusText);
        const errorData = await response.text();
        console.error('Error response:', errorData);
      }
    } catch (error) {
      console.error('Error fetching summary data:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchChartData = async () => {
    setChartsLoading(true);
    try {
      const currentUser = getCurrentUser();
      const params = new URLSearchParams({
        user_id: userId.toString(),
        date_filter: dateFilter,
        user_type: currentUser?.user_type || userRole, // Use user_type from session for precise table identification
      });

      // Untuk admin, bisa filter berdasarkan cabang
      if (userRole === 'admin' && selectedBranch !== 'all') {
        params.append('branch_id', selectedBranch.toString());
      }
      // Untuk branch, selalu gunakan branch_id mereka
      else if (userRole === 'branch' && branchId) {
        params.append('branch_id', branchId.toString());
      }

      // Fetch transaction time chart data
      const timeResponse = await fetch(`${config.apiUrl}/dashboard/transaction-time-chart?${params}`);
      if (timeResponse.ok) {
        const timeData = await timeResponse.json();
        setTimeSlotData(timeData);
      }

      // Fetch revenue vs expenses chart data
      const revenueParams = new URLSearchParams(params);
      revenueParams.set('date_filter', 'month'); // Always use month for revenue chart
      const revenueResponse = await fetch(`${config.apiUrl}/dashboard/revenue-expenses-chart?${revenueParams}`);
      if (revenueResponse.ok) {
        const revenueData = await revenueResponse.json();
        setRevenueExpenseData(revenueData);
      }
    } catch (error) {
      console.error('Error fetching chart data:', error);
    } finally {
      setChartsLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      day: '2-digit',
      month: 'short',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Chart configurations
  const timeSlotChartData = {
    labels: timeSlotData.map(item => item.time_slot),
    datasets: [
      {
        label: 'Jumlah Transaksi',
        data: timeSlotData.map(item => item.transaction_count),
        backgroundColor: 'rgba(59, 130, 246, 0.5)',
        borderColor: 'rgba(59, 130, 246, 1)',
        borderWidth: 1,
      },
    ],
  };

  const timeSlotChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: 'Distribusi Transaksi per Jam',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          stepSize: 1,
        },
      },
    },
  };

  const revenueExpenseChartData = {
    labels: revenueExpenseData.map(item => {
      const date = new Date(item.date);
      return date.toLocaleDateString('id-ID', { month: 'short', day: 'numeric' });
    }),
    datasets: [
      {
        label: 'Pendapatan',
        data: revenueExpenseData.map(item => item.revenue),
        borderColor: 'rgba(34, 197, 94, 1)',
        backgroundColor: 'rgba(34, 197, 94, 0.1)',
        tension: 0.1,
      },
      {
        label: 'Pengeluaran',
        data: revenueExpenseData.map(item => item.expenses),
        borderColor: 'rgba(239, 68, 68, 1)',
        backgroundColor: 'rgba(239, 68, 68, 0.1)',
        tension: 0.1,
      },
      {
        label: 'Profit',
        data: revenueExpenseData.map(item => item.profit),
        borderColor: 'rgba(59, 130, 246, 1)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.1,
      },
    ],
  };

  const revenueExpenseChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: 'Pendapatan vs Pengeluaran (30 Hari Terakhir)',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          callback: function (value: any) {
            return new Intl.NumberFormat('id-ID', {
              style: 'currency',
              currency: 'IDR',
              minimumFractionDigits: 0,
            }).format(value);
          },
        },
      },
    },
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header dengan Filter */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-neutral-900">Dashboard Ringkasan</h1>
          <p className="text-neutral-600">
            {userRole === 'admin' ? 'Ringkasan data seluruh sistem' : 'Ringkasan data cabang Anda'}
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-3">
          {/* Filter Tanggal */}
          <select
            value={dateFilter}
            onChange={(e) => setDateFilter(e.target.value)}
            className="px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          >
            <option value="today">Hari Ini</option>
            <option value="week">Minggu Ini</option>
            <option value="month">Bulan Ini</option>
            <option value="year">Tahun Ini</option>
          </select>

          {/* Filter Cabang - Hanya untuk Admin */}
          {userRole === 'admin' && (
            <select
              value={selectedBranch}
              onChange={(e) => setSelectedBranch(e.target.value === 'all' ? 'all' : parseInt(e.target.value))}
              className="px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="all">Semua Cabang</option>
              {branches.map((branch) => (
                <option key={branch.id} value={branch.id}>
                  {branch.name}
                </option>
              ))}
            </select>
          )}
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-4 border-primary-500 border-t-transparent"></div>
        </div>
      ) : summaryData ? (
        <>
          {/* Cards Ringkasan */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Total Penjualan */}
            <div className="bg-white rounded-lg shadow-sm p-6 border border-neutral-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-600">Total Penjualan</p>
                  <p className="text-2xl font-bold text-neutral-900">
                    {formatCurrency(summaryData.totalSales)}
                  </p>
                </div>
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <DollarSign size={24} className="text-green-600" />
                </div>
              </div>
            </div>

            {/* Total Transaksi */}
            <div className="bg-white rounded-lg shadow-sm p-6 border border-neutral-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-600">Total Transaksi</p>
                  <p className="text-2xl font-bold text-neutral-900">
                    {summaryData.totalTransactions.toLocaleString('id-ID')}
                  </p>
                </div>
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <ShoppingCart size={24} className="text-blue-600" />
                </div>
              </div>
            </div>

            {/* Total Pelanggan */}
            <div className="bg-white rounded-lg shadow-sm p-6 border border-neutral-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-600">Total Pelanggan</p>
                  <p className="text-2xl font-bold text-neutral-900">
                    {summaryData.totalCustomers.toLocaleString('id-ID')}
                  </p>
                </div>
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <Users size={24} className="text-purple-600" />
                </div>
              </div>
            </div>

            {/* Total Produk */}
            <div className="bg-white rounded-lg shadow-sm p-6 border border-neutral-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-600">Total Produk</p>
                  <p className="text-2xl font-bold text-neutral-900">
                    {summaryData.totalProducts.toLocaleString('id-ID')}
                  </p>
                </div>
                <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                  <BarChart3 size={24} className="text-orange-600" />
                </div>
              </div>
            </div>
          </div>

          {/* Statistik Periode */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Penjualan Hari Ini */}
            <div className="bg-white rounded-lg shadow-sm p-6 border border-neutral-200">
              <h3 className="text-lg font-semibold text-neutral-900 mb-4">Penjualan Hari Ini</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-neutral-600">Nilai Penjualan:</span>
                  <span className="font-semibold">{formatCurrency(summaryData.todaySales)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-neutral-600">Jumlah Transaksi:</span>
                  <span className="font-semibold">{summaryData.todayTransactions}</span>
                </div>
              </div>
            </div>

            {/* Penjualan Bulan Ini */}
            <div className="bg-white rounded-lg shadow-sm p-6 border border-neutral-200">
              <h3 className="text-lg font-semibold text-neutral-900 mb-4">Penjualan Bulan Ini</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-neutral-600">Nilai Penjualan:</span>
                  <span className="font-semibold">{formatCurrency(summaryData.monthSales)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-neutral-600">Jumlah Transaksi:</span>
                  <span className="font-semibold">{summaryData.monthTransactions}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Produk Terlaris dan Transaksi Terbaru */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Produk Terlaris */}
            <div className="bg-white rounded-lg shadow-sm p-6 border border-neutral-200">
              <h3 className="text-lg font-semibold text-neutral-900 mb-4">Produk Terlaris</h3>
              <div className="space-y-3">
                {summaryData.topProducts && summaryData.topProducts.length > 0 ? (
                  summaryData.topProducts.slice(0, 5).map((product, index) => (
                    <div key={product.id} className="flex items-center justify-between p-3 bg-neutral-50 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center text-primary-600 font-semibold text-sm">
                          {index + 1}
                        </div>
                        <div>
                          <p className="font-medium text-neutral-900">{product.name}</p>
                          <p className="text-sm text-neutral-600">{product.quantity} terjual</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold text-neutral-900">{formatCurrency(product.sales)}</p>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-neutral-500 text-center py-4">Belum ada data produk</p>
                )}
              </div>
            </div>

            {/* Transaksi Terbaru */}
            <div className="bg-white rounded-lg shadow-sm p-6 border border-neutral-200">
              <h3 className="text-lg font-semibold text-neutral-900 mb-4">Transaksi Terbaru</h3>
              <div className="space-y-3">
                {summaryData.recentTransactions && summaryData.recentTransactions.length > 0 ? (
                  summaryData.recentTransactions.slice(0, 5).map((transaction) => (
                    <div key={transaction.id} className="flex items-center justify-between p-3 bg-neutral-50 rounded-lg">
                      <div>
                        <p className="font-medium text-neutral-900">#{transaction.id}</p>
                        <p className="text-sm text-neutral-600">{transaction.customer_name}</p>
                        <p className="text-xs text-neutral-500">{formatDate(transaction.date)}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold text-neutral-900">{formatCurrency(transaction.total)}</p>
                        <p className="text-xs text-neutral-600">{transaction.payment_method}</p>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-neutral-500 text-center py-4">Belum ada transaksi</p>
                )}
              </div>
            </div>
          </div>

          {/* Charts Section */}
          <div className="grid grid-cols-1 gap-6">
            {/* Transaction Time Chart */}
            <div className="bg-white rounded-lg shadow-sm p-6 border border-neutral-200">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-neutral-900">Jam Transaksi Terbanyak</h3>
                <div className="p-2 bg-blue-100 rounded-full">
                  <Clock size={20} className="text-blue-600" />
                </div>
              </div>
              {chartsLoading ? (
                <div className="flex justify-center items-center h-80">
                  <div className="animate-spin rounded-full h-8 w-8 border-2 border-primary-500 border-t-transparent"></div>
                </div>
              ) : timeSlotData.length > 0 ? (
                <div className="h-80 w-full">
                  <Bar data={timeSlotChartData} options={timeSlotChartOptions} />
                </div>
              ) : (
                <div className="flex justify-center items-center h-80 text-neutral-500">
                  Belum ada data transaksi
                </div>
              )}
            </div>

            {/* Revenue vs Expenses Chart */}
            <div className="bg-white rounded-lg shadow-sm p-6 border border-neutral-200">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-neutral-900">Pendapatan vs Pengeluaran</h3>
                <div className="p-2 bg-green-100 rounded-full">
                  <TrendingUp size={20} className="text-green-600" />
                </div>
              </div>
              {chartsLoading ? (
                <div className="flex justify-center items-center h-80">
                  <div className="animate-spin rounded-full h-8 w-8 border-2 border-primary-500 border-t-transparent"></div>
                </div>
              ) : revenueExpenseData.length > 0 ? (
                <div className="h-80 w-full">
                  <Line data={revenueExpenseChartData} options={revenueExpenseChartOptions} />
                </div>
              ) : (
                <div className="flex justify-center items-center h-80 text-neutral-500">
                  Belum ada data pendapatan/pengeluaran
                </div>
              )}
            </div>
          </div>
        </>
      ) : (
        <div className="text-center py-12">
          <p className="text-neutral-500">Tidak ada data untuk ditampilkan</p>
          <p className="text-sm text-neutral-400 mt-2">
            {userRole === 'branch' ? 'Belum ada transaksi di cabang Anda' : 'Belum ada data tersedia'}
          </p>
        </div>
      )}
    </div>
  );
};

export default DashboardSummary;
