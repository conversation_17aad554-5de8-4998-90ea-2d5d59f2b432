import React, { useState, useEffect, useRef } from 'react';
import {
  ArrowLeft,
  Search,
  FileText,
  Download,
  X,
  Printer,
  Bluetooth,
  CheckCircle,
  Clock,
  XCircle,
  Calendar
} from 'lucide-react';
import { Link } from 'react-router-dom';
import * as XLSX from 'xlsx';
import config from '../config';
import BluetoothPrinter from '../utils/BluetoothPrinter';
import { jsPDF } from 'jspdf';
import autoTable from 'jspdf-autotable';
import { fetchWithSession, getCurrentUser } from '../utils/api';

interface TransactionItem {
  id?: number;
  transaction_id: string;
  product_id: number; // Tambahkan product_id
  product_name: string;
  price: number;
  quantity: number;
}

interface Transaction {
  id: string;
  date: string;
  customer_name: string;
  branch_id: number | null;
  branch_name: string | null;
  subtotal: number;
  tax: number;
  total: number;
  amount_paid: number;
  change_amount: number;
  payment_method: string;
  payment_method_id: number | null;
  payment_method_name?: string;
  payment_method_type?: string;
  payment_status: 'paid' | 'pending' | 'cancelled'; // Tambahkan status pembayaran
  items: TransactionItem[];
  cashier_name?: string; // Add cashier_name to the Transaction interface
  // Detail metode pembayaran
  bank_name?: string | null;
  account_number?: string | null;
  account_name?: string | null;
  qris_image?: string | null;
  qris_image_url?: string | null;
  wallet_provider?: string | null;
  wallet_number?: string | null;
  // Gateway info untuk QR code dari Midtrans
  gateway_info?: {
    gateway_transaction_id: string;
    gateway_provider: string;
    payment_type: string;
    status: string;
    qr_code_url?: string;
    expiry_time?: string;
    transaction_status?: string;
  } | null;
}

// Tambahkan interface untuk Branch
interface Branch {
  id: number;
  name: string;
}

// Tambahkan interface untuk StoreConfig
interface StoreConfig {
  store_name: string;
  // properti lain bisa ditambahkan sesuai kebutuhan
}

const TransactionHistory: React.FC = () => {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [filteredTransactions, setFilteredTransactions] = useState<Transaction[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [dateFilter, setDateFilter] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const printRef = useRef<HTMLDivElement>(null);
  const [isPrinterConnected, setIsPrinterConnected] = useState<boolean>(false);
  const [isPrinting, setIsPrinting] = useState<boolean>(false);
  const [showPrintOptions, setShowPrintOptions] = useState(false);
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [successStatus, setSuccessStatus] = useState<'paid' | 'pending' | 'cancelled'>('paid');

  // Tambahkan state untuk filter cabang
  const [branches, setBranches] = useState<Branch[]>([]);
  const [selectedBranchId, setSelectedBranchId] = useState<string>('');

  // Tambahkan state untuk filter status
  const [statusFilter, setStatusFilter] = useState<string>('');

  const currentUser = getCurrentUser();
  const isAdmin = currentUser?.role === 'admin';
  const branchId = currentUser?.id;

  // Fungsi untuk mengambil data cabang
  const fetchBranches = async () => {
    try {
      // Tambahkan user_id sebagai query parameter untuk hanya mengambil cabang milik admin ini
      const response = await fetchWithSession(`${config.apiUrl}/branches?user_id=${currentUser.id}`);
      if (response && response.ok) {
        const data = await response.json();
        setBranches(data);
      }
    } catch (error) { }
  };

  const fetchTransactions = async () => {
    setIsLoading(true);
    try {
      let url = `${config.apiUrl}/transactions`;

      // Jika bukan admin, hanya tampilkan transaksi cabang sendiri
      if (!isAdmin) {
        if (currentUser?.role === 'cashier') {
          url = `${config.apiUrl}/transactions?cashier_id=${currentUser.id}`;
        } else {
          url = `${config.apiUrl}/transactions/branch/${branchId}`;
        }
      }
      // Jika admin dan ada filter cabang yang dipilih
      else if (isAdmin && selectedBranchId) {
        url = `${config.apiUrl}/transactions/branch/${selectedBranchId}`;
      }
      // Jika admin tanpa filter cabang, tampilkan transaksi admin dan cabang-cabangnya
      else if (isAdmin) {
        url = `${config.apiUrl}/transactions?user_id=${currentUser.id}`;
      }

      // Tambahkan filter tanggal jika ada
      if (dateFilter) {
        const formattedDate = formatDateForAPI(dateFilter);
        url += `${url.includes('?') ? '&' : '?'}date=${formattedDate}`;
      }

      const response = await fetchWithSession(url);
      if (response && response.ok) {
        const data = await response.json();
        setTransactions(data);
      } else {
        setTransactions([]);
      }
    } catch (error) {
      setTransactions([]);
    } finally {
      setIsLoading(false);
    }
  };

  // State untuk konfigurasi toko
  const [storeConfig, setStoreConfig] = useState<StoreConfig>({ store_name: 'Satu Lisan' });

  // Fungsi untuk mengambil konfigurasi toko
  const fetchStoreConfig = async () => {
    try {
      const currentUser = getCurrentUser();
      const userId = currentUser?.id;

      if (!userId) return;

      const response = await fetchWithSession(`${config.apiUrl}/store-config?user_id=${userId}`);
      if (response && response.ok) {
        const data = await response.json();
        setStoreConfig(data);
      }
    } catch (error) {
      // Handle error silently
    }
  };

  useEffect(() => {
    // Ambil data cabang hanya jika user adalah admin
    if (isAdmin) {
      fetchBranches();
    }

    fetchTransactions();
    fetchStoreConfig(); // Tambahkan pemanggilan fungsi ini
  }, [isAdmin, branchId, selectedBranchId]); // Tambahkan selectedBranchId sebagai dependency

  useEffect(() => {
    setFilteredTransactions(transactions.filter(transaction => {
      const matchesSearch =
        transaction.customer_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        transaction.id.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesDate = dateFilter ? transaction.date.includes(dateFilter) : true;

      // Tambahkan filter berdasarkan status
      const matchesStatus = statusFilter ? transaction.payment_status === statusFilter : true;

      return matchesSearch && matchesDate && matchesStatus;
    }));
  }, [transactions, searchQuery, dateFilter, statusFilter]);

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(price);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Fungsi untuk memformat tanggal untuk API
  const formatDateForAPI = (dateString: string): string => {
    // Pastikan format tanggal adalah YYYY-MM-DD
    // Jika sudah dalam format yang benar, kembalikan langsung
    if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
      return dateString;
    }

    // Jika format lain, konversi ke YYYY-MM-DD
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  };

  // Fungsi untuk menampilkan detail transaksi
  const handleViewDetails = async (transaction: Transaction) => {
    try {
      // Fetch detailed transaction data
      const response = await fetchWithSession(`${config.apiUrl}/transactions/${transaction.id}`);
      if (response && response.ok) {
        const detailedTransaction = await response.json();

        // Pastikan items selalu ada, jika tidak ada, berikan array kosong
        if (!detailedTransaction.items || !Array.isArray(detailedTransaction.items)) {
          detailedTransaction.items = [];
        }

        // Pastikan tipe data yang benar
        setSelectedTransaction(detailedTransaction as Transaction);
        setShowDetailModal(true);
      } else {
        alert('Gagal mengambil detail transaksi');
      }
    } catch (error) {
      alert('Gagal mengambil detail transaksi');
    }
  };

  // Fungsi untuk menghubungkan ke printer Bluetooth
  const connectToPrinter = async () => {
    try {
      if (!BluetoothPrinter.isSupported()) {
        alert('Bluetooth tidak didukung di perangkat ini');
        return;
      }

      const connected = await BluetoothPrinter.connect();
      setIsPrinterConnected(connected);

      if (connected) {
        alert('Berhasil terhubung ke printer Bluetooth');
      } else {
        alert('Gagal terhubung ke printer Bluetooth');
      }
    } catch (error) {
      alert('Gagal terhubung ke printer: ' + (error instanceof Error ? error.message : 'Unknown error'));
    }
  };

  // Fungsi untuk mencetak ke printer Bluetooth
  const printToBluetoothPrinter = async () => {
    if (!selectedTransaction) return;
    setShowPrintOptions(false);

    try {
      setIsPrinting(true);

      if (!isPrinterConnected) {
        const connected = await BluetoothPrinter.connect();
        setIsPrinterConnected(connected);

        if (!connected) {
          alert('Gagal terhubung ke printer Bluetooth. Pastikan printer dinyalakan dan dalam mode pairing.');
          setIsPrinting(false);
          return;
        }
      }

      // Tambahkan store_name ke objek transaksi
      const transactionWithStoreName = {
        ...selectedTransaction,
        store_name: storeConfig.store_name
      };

      // Gunakan method printWithFallback yang mencoba beberapa format
      const success = await BluetoothPrinter.printWithFallback(transactionWithStoreName);

      if (success) {
        alert('Struk berhasil dicetak');
      } else {
        alert('Gagal mencetak struk. Pastikan printer terhubung dengan benar dan dalam keadaan siap. Periksa console untuk detail error.');
      }
    } catch (error) {
      alert('Gagal mencetak: ' + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setIsPrinting(false);
    }
  };

  // Fungsi untuk mencetak ke PDF
  const printToPDF = () => {
    if (!selectedTransaction) return;
    setShowPrintOptions(false);

    try {
      setIsPrinting(true);

      const doc = new jsPDF();

      // Header
      doc.setFontSize(18);
      doc.text(storeConfig.store_name, 105, 15, { align: 'center' });
      doc.setFontSize(12);
      doc.text(selectedTransaction.branch_name || 'Cabang Utama', 105, 22, { align: 'center' });
      doc.text(formatDate(selectedTransaction.date), 105, 28, { align: 'center' });

      // Transaction info
      doc.setFontSize(10);
      doc.text(`ID Transaksi: ${selectedTransaction.id}`, 14, 40);
      doc.text(`Pelanggan: ${selectedTransaction.customer_name}`, 14, 46);
      doc.text(`Metode Pembayaran: ${selectedTransaction.payment_method}`, 14, 52);

      // Tambahkan detail metode pembayaran jika ada
      let paymentDetailY = 58;
      if (selectedTransaction.payment_method_type === 'bank_transfer') {
        doc.text(`Bank: ${selectedTransaction.bank_name || '-'}`, 14, paymentDetailY);
        paymentDetailY += 6;
        doc.text(`No. Rekening: ${selectedTransaction.account_number || '-'}`, 14, paymentDetailY);
        paymentDetailY += 6;
        doc.text(`Atas Nama: ${selectedTransaction.account_name || '-'}`, 14, paymentDetailY);
        paymentDetailY += 6;
      } else if (selectedTransaction.payment_method_type === 'e_wallet') {
        doc.text(`Provider: ${selectedTransaction.wallet_provider || '-'}`, 14, paymentDetailY);
        paymentDetailY += 6;
        doc.text(`Nomor: ${selectedTransaction.wallet_number || '-'}`, 14, paymentDetailY);
        paymentDetailY += 6;
      }

      // Items table
      const tableColumn = ["Produk", "Qty", "Harga", "Total"];
      const tableRows = [] as any[][];

      if (Array.isArray(selectedTransaction.items)) {
        selectedTransaction.items.forEach(item => {
          const itemData = [
            item.product_name,
            item.quantity,
            formatPrice(item.price),
            formatPrice(item.price * item.quantity)
          ];
          tableRows.push(itemData);
        });
      }

      // Gunakan autoTable yang diimpor secara langsung
      autoTable(doc, {
        head: [tableColumn],
        body: tableRows,
        startY: paymentDetailY,
        theme: 'grid',
        styles: { fontSize: 9 },
        headStyles: { fillColor: [66, 135, 245] }
      });

      const finalY = (doc as any).lastAutoTable.finalY + 10;

      // Summary
      doc.text(`Subtotal: ${formatPrice(selectedTransaction.subtotal)}`, 140, finalY, { align: 'right' });
      doc.text(`Pajak: ${formatPrice(selectedTransaction.tax)}`, 140, finalY + 6, { align: 'right' });
      doc.text(`Total: ${formatPrice(selectedTransaction.total)}`, 140, finalY + 12, { align: 'right' });
      doc.text(`Dibayar: ${formatPrice(selectedTransaction.amount_paid)}`, 140, finalY + 18, { align: 'right' });
      doc.text(`Kembalian: ${formatPrice(selectedTransaction.change_amount)}`, 140, finalY + 24, { align: 'right' });

      // Footer
      doc.setFontSize(10);
      doc.text('Terima kasih atas kunjungan Anda!', 105, finalY + 36, { align: 'center' });
      doc.text('Silahkan berkunjung kembali', 105, finalY + 42, { align: 'center' });

      // Save the PDF
      doc.save(`transaksi-${selectedTransaction.id}.pdf`);

      alert('PDF berhasil dibuat dan diunduh');
      setIsPrinting(false);
    } catch (error) {
      alert('Gagal membuat PDF: ' + (error instanceof Error ? error.message : 'Unknown error'));
      setIsPrinting(false);
    }
  };

  // Fungsi untuk menampilkan opsi cetak
  const handlePrintOptions = () => {
    setShowPrintOptions(true);
  };

  const handleExportData = () => {
    // Persiapkan data untuk export Excel
    const exportData = filteredTransactions.map(transaction => ({
      'ID Transaksi': transaction.id,
      'Tanggal': formatDate(transaction.date),
      'Pelanggan': transaction.customer_name,
      'Metode Pembayaran': transaction.payment_method,
      'Subtotal': transaction.subtotal,
      'Pajak': transaction.tax,
      'Total': transaction.total,
      'Jumlah Item': Array.isArray(transaction.items)
        ? transaction.items.reduce((sum, item) => sum + item.quantity, 0)
        : 0, // Tambahkan pengecekan
    }));

    // Buat workbook baru
    const wb = XLSX.utils.book_new();

    // Buat worksheet dari data
    const ws = XLSX.utils.json_to_sheet(exportData);

    // Tambahkan worksheet ke workbook
    XLSX.utils.book_append_sheet(wb, ws, 'Transaksi');

    // Buat worksheet kedua untuk detail item per transaksi
    const detailsData: any[] = [];

    filteredTransactions.forEach(transaction => {
      // Tambahkan pengecekan untuk memastikan items ada dan merupakan array
      if (Array.isArray(transaction.items)) {
        transaction.items.forEach(item => {
          detailsData.push({
            'ID Transaksi': transaction.id,
            'Tanggal': formatDate(transaction.date),
            'Pelanggan': transaction.customer_name,
            'Nama Item': item.product_name,
            'Harga Satuan': item.price,
            'Jumlah': item.quantity,
            'Total Item': item.price * item.quantity
          });
        });
      }
    });

    // Buat worksheet detail
    const wsDetails = XLSX.utils.json_to_sheet(detailsData);

    // Tambahkan worksheet detail ke workbook
    XLSX.utils.book_append_sheet(wb, wsDetails, 'Detail Item');

    // Tentukan nama file
    const fileName = `transaksi-${new Date().toISOString().slice(0, 10)}.xlsx`;

    // Export file Excel
    XLSX.writeFile(wb, fileName);
  };

  // Tambahkan fungsi untuk mendapatkan warna status
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Tambahkan fungsi untuk mendapatkan label status
  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'paid':
        return 'Lunas';
      case 'pending':
        return 'Menunggu';
      case 'cancelled':
        return 'Dibatalkan';
      default:
        return 'Unknown';
    }
  };

  // Tambahkan fungsi untuk mengubah status pembayaran
  const handleUpdatePaymentStatus = async (newStatus: 'paid' | 'pending' | 'cancelled') => {
    if (!selectedTransaction) return;

    // Cek apakah transaksi berstatus menunggu, hanya status menunggu yang bisa diubah
    if (selectedTransaction.payment_status !== 'pending') {
      alert(`Transaksi dengan status ${getStatusLabel(selectedTransaction.payment_status)} tidak dapat diubah.`);
      return;
    }

    try {
      setIsUpdatingStatus(true);

      const response = await fetchWithSession(`${config.apiUrl}/transactions/${selectedTransaction.id}/payment-status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ payment_status: newStatus })
      });

      if (response?.ok) {
        // Update local state
        setSelectedTransaction({
          ...selectedTransaction,
          payment_status: newStatus
        });

        // Refresh transaction list
        fetchTransactions();

        // Tampilkan modal sukses
        setSuccessStatus(newStatus);
        setSuccessMessage(`Status pembayaran berhasil diubah menjadi ${getStatusLabel(newStatus)}`);
        setShowSuccessModal(true);
      } else {
        const errorData = await response?.json();
        alert(`Gagal memperbarui status: ${errorData.error || 'Unknown error'}`);
      }
    } catch (error) {
      alert('Gagal memperbarui status pembayaran');
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  // Tambahkan fungsi untuk mendapatkan informasi metode pembayaran
  const getPaymentMethodInfo = (transaction: Transaction) => {
    if (!transaction) return null;

    // Tampilkan informasi berdasarkan tipe metode pembayaran
    switch (transaction.payment_method_type) {
      case 'bank_transfer':
        return (
          <div className="bg-neutral-50 p-3 rounded-md border border-neutral-200 text-sm mt-2">
            <p><span className="font-medium">Bank:</span> {transaction.bank_name || '-'}</p>
            <p><span className="font-medium">No. Rekening:</span> {transaction.account_number || '-'}</p>
            <p><span className="font-medium">Atas Nama:</span> {transaction.account_name || '-'}</p>
          </div>
        );
      case 'qris':
        return (
          <div className="bg-neutral-50 p-3 rounded-md border border-neutral-200 text-sm mt-2">
            <p className="mb-2 font-medium">QRIS Payment</p>

            {/* Tampilkan QR code dari gateway (Midtrans) jika ada */}
            {transaction.gateway_info?.qr_code_url ? (
              <div className="flex flex-col items-center">
                <p className="text-xs text-neutral-600 mb-2">QR Code dari {transaction.gateway_info.gateway_provider}</p>
                <div className="flex justify-center">
                  <img
                    src={transaction.gateway_info.qr_code_url}
                    alt="QRIS Gateway"
                    className="max-w-full h-auto max-h-48 border border-neutral-300 rounded-md"
                    onError={(e) => {
                      console.error('Error loading gateway QR code');
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                </div>
                {transaction.gateway_info.transaction_status && (
                  <p className="text-xs text-neutral-600 mt-2">
                    Status: {transaction.gateway_info.transaction_status}
                  </p>
                )}
              </div>
            ) : transaction.qris_image_url ? (
              /* Fallback ke QRIS image dari payment method jika tidak ada gateway */
              <div className="flex justify-center">
                <img
                  src={`${config.imageBaseUrl}/uploads/qris/${transaction.qris_image_url}`}
                  alt="QRIS"
                  className="max-w-full h-auto max-h-48 border border-neutral-300 rounded-md"
                />
              </div>
            ) : (
              <p className="text-neutral-500 text-center">QR Code tidak tersedia</p>
            )}
          </div>
        );
      case 'e_wallet':
        return (
          <div className="bg-neutral-50 p-3 rounded-md border border-neutral-200 text-sm mt-2">
            <p><span className="font-medium">Provider:</span> {transaction.wallet_provider || '-'}</p>
            <p><span className="font-medium">Nomor:</span> {transaction.wallet_number || '-'}</p>
          </div>
        );
      default:
        return null;
    }
  };

  // Tambahkan fungsi untuk mendapatkan label metode pembayaran
  const getPaymentMethodLabel = (transaction: Transaction): string => {
    if (!transaction) return 'Tunai';

    // Jika ada payment_method_name, gunakan itu
    if (transaction.payment_method_name) {
      return transaction.payment_method_name;
    }

    return transaction.payment_method || 'Tunai';
  };

  // Tambahkan fungsi untuk menutup modal sukses
  const closeSuccessModal = () => {
    setShowSuccessModal(false);
  };

  // Fungsi untuk menangani perubahan filter cabang
  const handleBranchFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedBranchId(e.target.value);
  };

  // Fungsi untuk menangani perubahan filter status
  const handleStatusFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setStatusFilter(e.target.value);
  };

  return (
    <div className="container mx-auto p-4">
      <div className="flex flex-col md:flex-row md:items-center justify-between mb-6 gap-4">
        <div className="flex items-center gap-3">
          <Link to="/" className="p-2 rounded-full hover:bg-neutral-100">
            <ArrowLeft size={20} />
          </Link>
          <h1 className="text-2xl font-bold text-primary-800">Riwayat Transaksi</h1>
        </div>
        <div className="flex flex-wrap gap-2">
          <button
            onClick={connectToPrinter}
            className={`flex items-center gap-2 ${isPrinterConnected ? 'bg-green-600' : 'bg-primary-600'
              } text-white py-2 px-4 rounded-md hover:bg-primary-700`}
          >
            <Bluetooth size={18} />
            <span>{isPrinterConnected ? 'Printer Terhubung' : 'Hubungkan Printer'}</span>
          </button>
          <button
            onClick={handleExportData}
            className="flex items-center gap-2 bg-primary-600 text-white py-2 px-4 rounded-md hover:bg-primary-700"
          >
            <Download size={18} />
            <span>Export Data</span>
          </button>
        </div>
      </div>

      <div className="bg-white rounded-xl shadow-sm overflow-hidden mb-6">
        <div className="p-4 border-b border-neutral-200">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-grow">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search size={18} className="text-neutral-400" />
              </div>
              <input
                type="text"
                placeholder="Cari transaksi..."
                className="pl-10 p-2 border border-neutral-300 rounded-md w-full focus:outline-none focus:ring-2 focus:ring-primary-500"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                autoComplete="off"
              />
            </div>

            {/* Filter Tanggal */}
            <div className="relative md:w-64">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Calendar size={18} className="text-neutral-400" />
              </div>
              <input
                type="date"
                className="pl-10 p-2 border border-neutral-300 rounded-md w-full focus:outline-none focus:ring-2 focus:ring-primary-500"
                value={dateFilter}
                onChange={(e) => setDateFilter(e.target.value)}
                autoComplete="off"
              />
            </div>

            {/* Filter Status */}
            <div className="relative md:w-64">
              <select
                className="p-2 border border-neutral-300 rounded-md w-full focus:outline-none focus:ring-2 focus:ring-primary-500 appearance-none"
                value={statusFilter}
                onChange={handleStatusFilterChange}
              >
                <option value="">Semua Status</option>
                <option value="paid">Lunas</option>
                <option value="pending">Menunggu</option>
                <option value="cancelled">Dibatalkan</option>
              </select>
              <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-neutral-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </div>
            </div>

            {/* Filter Cabang (hanya untuk admin) */}
            {isAdmin && (
              <div className="relative md:w-64">
                <select
                  className="p-2 border border-neutral-300 rounded-md w-full focus:outline-none focus:ring-2 focus:ring-primary-500 appearance-none"
                  value={selectedBranchId}
                  onChange={handleBranchFilterChange}
                >
                  <option value="">Semua Cabang</option>
                  {branches.map(branch => (
                    <option key={branch.id} value={branch.id}>
                      {branch.name}
                    </option>
                  ))}
                </select>
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <svg className="h-5 w-5 text-neutral-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            )}
          </div>
        </div>

        {isLoading ? (
          <div className="text-center py-8">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-primary-500 border-t-transparent"></div>
            <p className="mt-2 text-neutral-600">Memuat data transaksi...</p>
          </div>
        ) : filteredTransactions.length === 0 ? (
          <div className="text-center py-8 text-neutral-500">
            <p>Tidak ada transaksi yang ditemukan</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-neutral-200">
              <thead className="bg-neutral-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                    ID Transaksi
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                    Tanggal
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                    Pelanggan
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                    Cabang
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                    Kasir
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                    Total
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                    Metode Pembayaran
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-neutral-500 uppercase tracking-wider">
                    Aksi
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-neutral-200">
                {filteredTransactions.map((transaction) => (
                  <tr key={transaction.id} className="hover:bg-neutral-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-neutral-900">
                      {transaction.id}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-neutral-500">
                      {formatDate(transaction.date)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-neutral-500">
                      {transaction.customer_name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-neutral-500">
                      {transaction.branch_name || 'Cabang Utama'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-neutral-500">
                      {transaction.cashier_name || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-neutral-500">
                      {formatPrice(transaction.total)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-neutral-500">
                      {transaction.payment_method_name || transaction.payment_method}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(transaction.payment_status)}`}>
                        {getStatusLabel(transaction.payment_status)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => handleViewDetails(transaction)}
                        className="text-primary-600 hover:text-primary-900 mr-3"
                      >
                        Detail
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Transaction Detail Modal */}
      {showDetailModal && selectedTransaction && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto flex flex-col">
            {/* Header dengan tombol aksi */}
            <div className="sticky top-0 bg-white p-4 border-b border-neutral-200 flex justify-between items-center z-10">
              <h3 className="text-lg font-medium text-neutral-900">Detail Transaksi</h3>
              <div className="flex gap-2">
                <button
                  onClick={handlePrintOptions}
                  className="p-2 text-primary-600 hover:bg-primary-50 rounded-full flex items-center gap-1"
                  disabled={isPrinting}
                >
                  {isPrinting ? (
                    <>
                      <span className="animate-spin mr-1">⏳</span>
                      <span className="hidden xs:inline">Mencetak...</span>
                    </>
                  ) : (
                    <>
                      <Printer size={20} />
                      <span className="hidden sm:inline">Cetak</span>
                    </>
                  )}
                </button>
                <button
                  onClick={() => setShowDetailModal(false)}
                  className="p-2 text-neutral-600 hover:bg-neutral-100 rounded-full"
                >
                  <X size={20} />
                </button>
              </div>
            </div>

            {/* Konten receipt yang dapat di-scroll */}
            <div className="p-4 overflow-y-auto flex-1">
              <div ref={printRef} className="print-content">
                <div className="text-center mb-6">
                  <h2 className="text-xl font-bold">{storeConfig.store_name}</h2>
                  <p className="text-neutral-600">{selectedTransaction.branch_name || 'Cabang Utama'}</p>
                  <p className="text-neutral-600 text-sm">
                    {formatDate(selectedTransaction.date)}
                  </p>
                </div>

                <div className="mb-4">
                  <p><span className="font-medium">ID Transaksi:</span> {selectedTransaction.id}</p>
                  <p><span className="font-medium">Pelanggan:</span> {selectedTransaction.customer_name}</p>

                  {/* Status Pembayaran */}
                  <div className="mt-2 flex items-center">
                    <span className="font-medium mr-2">Status Pembayaran:</span>
                    <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(selectedTransaction.payment_status)}`}>
                      {getStatusLabel(selectedTransaction.payment_status)}
                    </span>
                  </div>

                  {/* Tombol ubah status untuk metode non-tunai */}
                  {selectedTransaction.payment_method_type !== 'cash' && (
                    <div className="mt-3 flex flex-wrap gap-2">
                      {/* Hanya tampilkan tombol jika status menunggu DAN tidak menggunakan payment gateway */}
                      {selectedTransaction.payment_status === 'pending' && !selectedTransaction.gateway_info && (
                        <>
                          <button
                            onClick={() => handleUpdatePaymentStatus('paid')}
                            disabled={isUpdatingStatus}
                            className="px-3 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full hover:bg-green-200 transition-colors"
                          >
                            {isUpdatingStatus ? 'Memproses...' : 'Tandai Lunas'}
                          </button>

                          <button
                            onClick={() => handleUpdatePaymentStatus('cancelled')}
                            disabled={isUpdatingStatus}
                            className="px-3 py-1 bg-red-100 text-red-800 text-xs font-medium rounded-full hover:bg-red-200 transition-colors"
                          >
                            {isUpdatingStatus ? 'Memproses...' : 'Tandai Dibatalkan'}
                          </button>
                        </>
                      )}

                      {/* Tampilkan pesan jika menggunakan payment gateway */}
                      {selectedTransaction.payment_status === 'pending' && selectedTransaction.gateway_info && (
                        <div className="px-3 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">
                          Status akan diperbarui otomatis oleh payment gateway
                        </div>
                      )}

                      {/* Tampilkan pesan jika status bukan menunggu */}
                      {selectedTransaction.payment_status !== 'pending' && (
                        <div className="px-3 py-1 bg-gray-100 text-gray-600 text-xs font-medium rounded-full">
                          Status {getStatusLabel(selectedTransaction.payment_status)}, tidak dapat diubah
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {/* Tabel item dengan overflow horizontal pada mobile */}
                <div className="overflow-x-auto mb-6">
                  <table className="w-full min-w-[400px]">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-2">Item</th>
                        <th className="text-center py-2">Qty</th>
                        <th className="text-right py-2">Harga</th>
                        <th className="text-right py-2">Total</th>
                      </tr>
                    </thead>
                    <tbody>
                      {/* Tambahkan pengecekan untuk memastikan items ada dan merupakan array */}
                      {Array.isArray(selectedTransaction.items) && selectedTransaction.items.length > 0 ? (
                        selectedTransaction.items.map((item, index) => (
                          <tr key={index} className="border-b border-dotted">
                            <td className="py-2">{item.product_name}</td>
                            <td className="text-center py-2">{item.quantity}x</td>
                            <td className="text-right py-2">{formatPrice(item.price)}</td>
                            <td className="text-right py-2">{formatPrice(item.price * item.quantity)}</td>
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td colSpan={4} className="py-4 text-center text-neutral-500">
                            Tidak ada item dalam transaksi ini
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>

                {/* Informasi total dengan layout yang responsif */}
                <div className="border-t pt-2 space-y-1">
                  <div className="flex justify-between py-1">
                    <span>Subtotal</span>
                    <span>{formatPrice(selectedTransaction.subtotal)}</span>
                  </div>
                  <div className="flex justify-between py-1">
                    <span>Pajak</span>
                    <span>{formatPrice(selectedTransaction.tax)}</span>
                  </div>
                  <div className="flex justify-between py-1 font-bold">
                    <span>Total</span>
                    <span>{formatPrice(selectedTransaction.total)}</span>
                  </div>
                  <div className="flex justify-between py-1">
                    <span>Dibayar</span>
                    <span>{formatPrice(selectedTransaction.amount_paid)}</span>
                  </div>
                  <div className="flex justify-between py-1">
                    <span>Kembalian</span>
                    <span>{formatPrice(selectedTransaction.change_amount)}</span>
                  </div>
                </div>

                {/* Informasi metode pembayaran */}
                <div className="mt-4 pt-3 border-t border-neutral-200">
                  <h4 className="font-medium mb-2">Metode Pembayaran</h4>
                  <p className="mb-2">{getPaymentMethodLabel(selectedTransaction)}</p>

                  {/* Tampilkan detail metode pembayaran */}
                  {getPaymentMethodInfo(selectedTransaction)}
                </div>

                <div className="text-center mt-6 text-neutral-500 text-sm">
                  <p>Terima kasih atas kunjungan Anda</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Print Options Modal */}
      {showPrintOptions && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60] p-4">
          <div className="bg-white rounded-lg w-full max-w-md p-6 animate-fade-in">
            <h3 className="text-lg font-medium text-neutral-900 mb-4">Pilih Metode Cetak</h3>

            <div className="space-y-3">
              <button
                onClick={printToBluetoothPrinter}
                className="w-full flex items-center justify-between p-4 border border-neutral-200 rounded-lg hover:bg-primary-50 hover:border-primary-200 transition-colors"
              >
                <div className="flex items-center gap-3">
                  <div className="bg-primary-100 p-2 rounded-full">
                    <Bluetooth size={20} className="text-primary-600" />
                  </div>
                  <div className="text-left">
                    <p className="font-medium text-neutral-800">Cetak Struk</p>
                    <p className="text-sm text-neutral-500">Cetak ke printer thermal Bluetooth</p>
                  </div>
                </div>
              </button>

              <button
                onClick={printToPDF}
                className="w-full flex items-center justify-between p-4 border border-neutral-200 rounded-lg hover:bg-primary-50 hover:border-primary-200 transition-colors"
              >
                <div className="flex items-center gap-3">
                  <div className="bg-primary-100 p-2 rounded-full">
                    <FileText size={20} className="text-primary-600" />
                  </div>
                  <div className="text-left">
                    <p className="font-medium text-neutral-800">Cetak PDF</p>
                    <p className="text-sm text-neutral-500">Unduh sebagai file PDF</p>
                  </div>
                </div>
              </button>
            </div>

            <div className="mt-6 flex justify-end">
              <button
                onClick={() => setShowPrintOptions(false)}
                className="px-4 py-2 text-neutral-600 hover:bg-neutral-100 rounded-md"
              >
                Batal
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Success Modal */}
      {showSuccessModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 animate-fade-in">
            <div className="flex flex-col items-center mb-6">
              <div className={`w-16 h-16 rounded-full flex items-center justify-center mb-4 ${successStatus === 'paid' ? 'bg-green-100' :
                successStatus === 'pending' ? 'bg-yellow-100' : 'bg-red-100'
                }`}>
                {successStatus === 'paid' && (
                  <CheckCircle size={32} className="text-green-500" />
                )}
                {successStatus === 'pending' && (
                  <Clock size={32} className="text-yellow-500" />
                )}
                {successStatus === 'cancelled' && (
                  <XCircle size={32} className="text-red-500" />
                )}
              </div>
              <h3 className="text-xl font-medium text-neutral-900">Status Diperbarui</h3>
              <p className="text-center text-neutral-600 mt-2">{successMessage}</p>
            </div>

            <div className="space-y-3 mb-6">
              <div className="flex justify-between text-sm">
                <span className="text-neutral-600">ID Transaksi:</span>
                <span className="font-medium">{selectedTransaction?.id}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-neutral-600">Pelanggan:</span>
                <span className="font-medium">{selectedTransaction?.customer_name}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-neutral-600">Total:</span>
                <span className="font-medium">{formatPrice(selectedTransaction?.total || 0)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-neutral-600">Status Baru:</span>
                <span className={`font-medium px-2 py-1 rounded-full text-xs ${successStatus === 'paid' ? 'bg-green-100 text-green-800' :
                  successStatus === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'
                  }`}>
                  {getStatusLabel(successStatus)}
                </span>
              </div>
            </div>

            <button
              onClick={closeSuccessModal}
              className={`w-full py-3 text-white rounded-md font-medium transition-colors ${successStatus === 'paid' ? 'bg-green-600 hover:bg-green-700' :
                successStatus === 'pending' ? 'bg-yellow-600 hover:bg-yellow-700' : 'bg-red-600 hover:bg-red-700'
                }`}
            >
              Tutup
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default TransactionHistory;


















