const pool = require('../config/database');

class PaymentGateway {
  static $table = 'payment_gateways';

  static async getByProvider(provider, userId = null) {
    try {
      let query = `SELECT * FROM ${this.$table} WHERE provider = ?`;
      const params = [provider];

      if (userId) {
        query += ' AND user_id = ?';
        params.push(userId);
      }

      const [rows] = await pool.query(query, params);
      return rows[0] || null;
    } catch (error) {
      console.error('Error getting payment gateway by provider:', error);
      throw error;
    }
  }
}

module.exports = PaymentGateway;




