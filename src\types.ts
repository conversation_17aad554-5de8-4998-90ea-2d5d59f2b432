export interface Category {
  id: string;
  value: string;
  label: string;
  icon: string;
  count?: number;
  userId?: number;
}

export interface Product {
  id: string;
  name: string;
  price: number;
  costPrice?: number;
  image: string;
  category: string;
  categoryLabel: string;
  userId?: number;
  branchId?: number;
}

export interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  image: string;
}

export interface CartItem {
  id: string;
  menuItem?: MenuItem;
  name?: string;
  price?: number;
  image?: string;
  quantity: number;
}

export interface Branch {
  id: string;
  name: string;
  address: string;
  phone: string;
  manager: string;
  isActive: boolean;
  email: string;
  password: string;
}

export interface PaymentInfo {
  subtotal: number;
  tax: number;
  total: number;
  amountPaid: number;
  change: number;
}

export interface Member {
  id: number;
  name: string;
  phone: string;
  email: string | null;
  address: string | null;
  birthdate: string | null;
  is_active: number;
  notes: string | null;
  user_id: number;
  created_at: string;
  updated_at: string;
}

export interface PaymentMethod {
  id: number;
  name: string;
  type: 'cash' | 'bank_transfer' | 'card' | 'qris' | 'e_wallet';
  description: string;
  account_number?: string;
  account_name?: string;
  bank_name?: string;
  qris_image?: string;
  qris_image_url?: string;
  wallet_provider?: string;
  wallet_number?: string;
  is_active: number;
  use_gateway?: number;
  user_id: number;
}
