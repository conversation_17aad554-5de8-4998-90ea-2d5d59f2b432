// Utility untuk mengelola koneksi dan printing ke printer Bluetooth
class BluetoothPrinterClass {
  private device: BluetoothDevice | null = null;
  private characteristic: BluetoothRemoteGATTCharacteristic | null = null;
  private isConnecting: boolean = false;

  // Cek apakah Web Bluetooth API tersedia
  public isSupported(): boolean {
    return 'bluetooth' in navigator && 'getAvailability' in navigator.bluetooth;
  }

  // Menghubungkan ke printer Bluetooth
  public async connect(): Promise<boolean> {
    if (this.isConnecting) {
      return false;
    }

    if (!this.isSupported()) {
      return false;
    }

    try {
      this.isConnecting = true;

      // Check if Bluetooth is available using try-catch instead of getAvailability
      let isAvailable = true;
      try {
        // Simple check by attempting to access the Bluetooth API
        if (!navigator.bluetooth) {
          isAvailable = false;
        }
      } catch (error) {
        isAvailable = false;
      }
      if (!isAvailable) {
        return false;
      }

      // Daftar UUID service yang umum digunakan oleh printer thermal Bluetooth
      const serviceUUIDs = [
        '000018f0-0000-1000-8000-00805f9b34fb', // UUID umum untuk beberapa printer
        '49535343-fe7d-4ae5-8fa9-9fafd205e455', // UUID untuk printer ESC/POS
        '0000ff00-0000-1000-8000-00805f9b34fb', // UUID alternatif
        '0000180a-0000-1000-8000-00805f9b34fb', // Device Information Service
        '00001101-0000-1000-8000-00805f9b34fb'  // Serial Port Profile
      ];

      // Gunakan salah satu dari filters ATAU acceptAllDevices, tidak keduanya
      // this.device = await navigator.bluetooth.requestDevice({
      //   // Gunakan filters untuk printer yang umum digunakan
      //   filters: [
      //     { namePrefix: 'Printer' },
      //     { namePrefix: 'POS' },
      //     { namePrefix: 'BT' },
      //     { namePrefix: 'TP' }
      //   ],
      //   optionalServices: serviceUUIDs
      // });

      // Alternatif: Gunakan acceptAllDevices jika filter tidak berhasil menemukan printer
      // Hapus komentar kode di bawah dan komentar kode di atas jika printer tidak terdeteksi
      this.device = await navigator.bluetooth.requestDevice({
        acceptAllDevices: true,
        optionalServices: serviceUUIDs
      });

      if (!this.device) {
        return false;
      }

      // Menghubungkan ke GATT server
      const server = await this.device.gatt?.connect();
      if (!server) {
        return false;
      }

      // Try each service UUID
      const services = [];
      for (const serviceUUID of serviceUUIDs) {
        try {
          const service = await server.getPrimaryService(serviceUUID);
          services.push(service);
        } catch (error) { }
      }

      // Coba setiap service yang tersedia
      let foundCharacteristic = false;
      for (const service of services) {
        try {
          // Get all available characteristics manually since getCharacteristics() doesn't exist
          const characteristics = [];
          // Common characteristic UUIDs for printer communication
          const characteristicUUIDs = [
            '00002af1-0000-1000-8000-00805f9b34fb', // Common printer characteristic
            '00002af0-0000-1000-8000-00805f9b34fb', // Alternative characteristic
            '*************-43f4-a8d4-ecbe34729bb3'  // ESC/POS characteristic
          ];

          for (const charUUID of characteristicUUIDs) {
            try {
              const characteristic = await service.getCharacteristic(charUUID);
              characteristics.push(characteristic);
            } catch (error) { }
          }

          // Coba setiap characteristic yang tersedia
          for (const characteristic of characteristics) {
            if (characteristic.properties.write || characteristic.properties.writeWithoutResponse) {
              this.characteristic = characteristic;
              foundCharacteristic = true;
              break;
            }
          }

          if (foundCharacteristic) break;
        } catch (error) {
          console.warn(`Gagal mengakses service ${service.uuid}:`, error);
          continue;
        }
      }

      if (!foundCharacteristic) {
        this.disconnect();
        return false;
      }

      return true;
    } catch (error) {
      return false;
    } finally {
      this.isConnecting = false;
    }
  }

  // Memutuskan koneksi dari printer
  public disconnect(): void {
    if (this.device && this.device.gatt?.connected) {
      this.device.gatt.disconnect();
    }
    this.device = null;
    this.characteristic = null;
  }

  // Memeriksa apakah terhubung ke printer
  public isConnected(): boolean {
    return this.device !== null && this.device.gatt?.connected === true;
  }

  // Mengirim data ke printer
  public async print(data: string): Promise<boolean> {
    if (!this.isConnected() || !this.characteristic) {
      console.warn('BluetoothPrinter: Printer not connected or characteristic not available');
      return false;
    }

    try {
      // Log full receipt data for debugging
      console.log('BluetoothPrinter: Full receipt data:');
      console.log(data);
      console.log('BluetoothPrinter: Receipt length:', data.length, 'characters');

      // Konversi string ke ArrayBuffer
      const encoder = new TextEncoder();
      const dataBuffer = encoder.encode(data);

      // Perkecil chunk size untuk printer yang sensitif
      const CHUNK_SIZE = 256; // Ukuran potongan lebih kecil untuk kompatibilitas
      console.log(`BluetoothPrinter: Sending ${dataBuffer.length} bytes in chunks of ${CHUNK_SIZE}`);

      let totalSent = 0;
      for (let i = 0; i < dataBuffer.length; i += CHUNK_SIZE) {
        const chunk = dataBuffer.slice(i, i + CHUNK_SIZE);
        totalSent += chunk.length;

        try {
          // Selalu gunakan writeValue untuk kompatibilitas yang lebih baik
          await this.characteristic.writeValue(chunk);

          console.log(`BluetoothPrinter: Sent chunk ${Math.floor(i / CHUNK_SIZE) + 1}/${Math.ceil(dataBuffer.length / CHUNK_SIZE)} (${totalSent}/${dataBuffer.length} bytes)`);

          // Tunggu lebih lama untuk memberikan waktu printer memproses data
          await new Promise(resolve => setTimeout(resolve, 200));
        } catch (chunkError) {
          console.error(`BluetoothPrinter: Failed to send chunk ${Math.floor(i / CHUNK_SIZE) + 1}:`, chunkError);
          throw chunkError;
        }
      }

      // Tunggu tambahan untuk memastikan semua data terproses
      await new Promise(resolve => setTimeout(resolve, 500));

      console.log('BluetoothPrinter: Print completed successfully');
      return true;
    } catch (error) {
      console.error('BluetoothPrinter: Print failed:', error);
      return false;
    }
  }

  // Validate and enhance transaction data
  private validateTransaction(transaction: any): any {
    // Ensure required fields have default values
    const validatedTransaction = {
      id: transaction.id || 'N/A',
      date: transaction.date || new Date().toISOString(),
      customer_name: transaction.customer_name || 'Umum',
      store_name: transaction.store_name || 'Satu Lisan',
      branch_name: transaction.branch_name || 'Cabang Utama',
      payment_method: transaction.payment_method || 'Tunai',
      payment_status: transaction.payment_status || 'paid',
      subtotal: Number(transaction.subtotal) || 0,
      tax: Number(transaction.tax) || 0,
      total: Number(transaction.total) || 0,
      amount_paid: Number(transaction.amount_paid) || 0,
      change_amount: Number(transaction.change_amount) || 0,
      items: Array.isArray(transaction.items) ? transaction.items : [],
      ...transaction // Keep all other properties
    };

    // Validate items
    validatedTransaction.items = validatedTransaction.items.map((item: any) => ({
      product_name: item.product_name || 'Produk Tidak Diketahui',
      quantity: Number(item.quantity) || 1,
      price: Number(item.price) || 0,
      ...item
    }));

    return validatedTransaction;
  }

  // Format receipt untuk printer thermal
  public formatReceipt(transaction: any): string {
    // Validate transaction data first
    const validTransaction = this.validateTransaction(transaction);
    let receipt = '';

    // Initialize printer (reset to default settings)
    receipt += '\x1B\x40'; // ESC @ - Initialize printer

    // Header
    receipt += '\x1B\x61\x01'; // Center align
    receipt += '\x1B\x21\x08'; // Double height text
    receipt += `${this.sanitizeText(validTransaction.store_name)}\n`;
    receipt += '\x1B\x21\x00'; // Normal text
    receipt += `${this.sanitizeText(validTransaction.branch_name)}\n`;

    // Store address if available
    if (validTransaction.store_address) {
      receipt += `${this.sanitizeText(validTransaction.store_address)}\n`;
    }
    if (validTransaction.store_phone) {
      receipt += `Tel: ${this.sanitizeText(validTransaction.store_phone)}\n`;
    }

    receipt += `${this.formatDate(validTransaction.date)}\n`;
    receipt += this.createLine(32) + '\n';

    // Transaction info
    receipt += '\x1B\x61\x00'; // Left align
    receipt += `No. Transaksi: ${this.sanitizeText(validTransaction.id)}\n`;
    receipt += `Pelanggan: ${this.sanitizeText(validTransaction.customer_name)}\n`;

    // Cashier info if available
    if (validTransaction.cashier_name) {
      receipt += `Kasir: ${this.sanitizeText(validTransaction.cashier_name)}\n`;
    }

    // Payment status
    const statusText = this.getPaymentStatusText(validTransaction.payment_status);
    receipt += `Status: ${statusText}\n`;

    receipt += `Metode Bayar: ${this.sanitizeText(validTransaction.payment_method)}\n`;

    // Payment method details
    if (validTransaction.payment_method_type === 'bank_transfer') {
      receipt += `Bank: ${this.sanitizeText(validTransaction.bank_name || '-')}\n`;
      receipt += `No. Rek: ${this.sanitizeText(validTransaction.account_number || '-')}\n`;
      receipt += `A/N: ${this.sanitizeText(validTransaction.account_name || '-')}\n`;
    } else if (validTransaction.payment_method_type === 'e_wallet') {
      receipt += `Provider: ${this.sanitizeText(validTransaction.wallet_provider || '-')}\n`;
      receipt += `Nomor: ${this.sanitizeText(validTransaction.wallet_number || '-')}\n`;
    }

    receipt += this.createLine(32) + '\n';

    // Items header
    receipt += 'ITEM                QTY   TOTAL\n';
    receipt += this.createLine(32) + '\n';

    // Items
    if (validTransaction.items.length > 0) {
      validTransaction.items.forEach((item: any) => {
        const productName = this.sanitizeText(item.product_name);
        const quantity = item.quantity;
        const price = item.price;
        const total = quantity * price;

        // Product name (truncate if too long)
        const maxNameLength = 20;
        const displayName = productName.length > maxNameLength
          ? productName.substring(0, maxNameLength - 3) + '...'
          : productName;

        receipt += `${displayName.padEnd(20)} ${String(quantity).padStart(3)} ${this.formatCurrency(total).padStart(8)}\n`;

        // Show unit price if different from total
        if (quantity > 1) {
          receipt += `  @ ${this.formatCurrency(price)} per item\n`;
        }
      });
    } else {
      receipt += 'Tidak ada item\n';
    }

    receipt += this.createLine(32) + '\n';

    // Totals with proper alignment
    const subtotal = validTransaction.subtotal;
    const tax = validTransaction.tax;
    const total = validTransaction.total;
    const amountPaid = validTransaction.amount_paid;
    const change = validTransaction.change_amount;

    receipt += `Subtotal:${this.formatCurrency(subtotal).padStart(23)}\n`;
    if (tax > 0) {
      receipt += `Pajak:${this.formatCurrency(tax).padStart(26)}\n`;
    }
    receipt += `${'TOTAL:'.padEnd(8)}${this.formatCurrency(total).padStart(24)}\n`;
    receipt += `Dibayar:${this.formatCurrency(amountPaid).padStart(24)}\n`;
    if (change > 0) {
      receipt += `Kembalian:${this.formatCurrency(change).padStart(22)}\n`;
    }

    receipt += this.createLine(32) + '\n';

    // Footer
    receipt += '\x1B\x61\x01'; // Center alignment
    receipt += 'Terima kasih atas kunjungan Anda!\n';
    receipt += 'Silahkan berkunjung kembali\n';

    // Additional footer info if available
    if (validTransaction.store_website) {
      receipt += `${this.sanitizeText(validTransaction.store_website)}\n`;
    }

    receipt += '\n\n\n';

    // Cut paper
    receipt += '\x1D\x56\x41\x10';

    return receipt;
  }

  // Format alternatif untuk printer yang tidak mendukung ESC/POS
  public formatPlainReceipt(transaction: any): string {
    // Validate transaction data first
    const validTransaction = this.validateTransaction(transaction);
    let receipt = '';

    // Header - centered manually
    const storeName = this.sanitizeText(validTransaction.store_name);
    const branchName = this.sanitizeText(validTransaction.branch_name);

    receipt += this.centerText(storeName, 32) + '\n';
    receipt += this.centerText(branchName, 32) + '\n';

    // Store address if available
    if (validTransaction.store_address) {
      receipt += this.centerText(this.sanitizeText(validTransaction.store_address), 32) + '\n';
    }
    if (validTransaction.store_phone) {
      receipt += this.centerText(`Tel: ${this.sanitizeText(validTransaction.store_phone)}`, 32) + '\n';
    }

    receipt += this.centerText(this.formatDate(validTransaction.date), 32) + '\n';
    receipt += this.createLine(32) + '\n';

    // Transaction info
    receipt += `No. Transaksi: ${this.sanitizeText(validTransaction.id)}\n`;
    receipt += `Pelanggan: ${this.sanitizeText(validTransaction.customer_name)}\n`;

    // Cashier info if available
    if (validTransaction.cashier_name) {
      receipt += `Kasir: ${this.sanitizeText(validTransaction.cashier_name)}\n`;
    }

    // Payment status
    const statusText = this.getPaymentStatusText(validTransaction.payment_status);
    receipt += `Status: ${statusText}\n`;

    receipt += `Metode Bayar: ${this.sanitizeText(validTransaction.payment_method)}\n`;

    // Payment method details
    if (validTransaction.payment_method_type === 'bank_transfer') {
      receipt += `Bank: ${this.sanitizeText(validTransaction.bank_name || '-')}\n`;
      receipt += `No. Rek: ${this.sanitizeText(validTransaction.account_number || '-')}\n`;
      receipt += `A/N: ${this.sanitizeText(validTransaction.account_name || '-')}\n`;
    } else if (validTransaction.payment_method_type === 'e_wallet') {
      receipt += `Provider: ${this.sanitizeText(validTransaction.wallet_provider || '-')}\n`;
      receipt += `Nomor: ${this.sanitizeText(validTransaction.wallet_number || '-')}\n`;
    }

    receipt += this.createLine(32) + '\n';

    // Items header
    receipt += 'ITEM                QTY   TOTAL\n';
    receipt += this.createLine(32) + '\n';

    // Items
    if (validTransaction.items.length > 0) {
      validTransaction.items.forEach((item: any) => {
        const productName = this.sanitizeText(item.product_name);
        const quantity = item.quantity;
        const price = item.price;
        const total = quantity * price;

        // Product name (truncate if too long)
        const maxNameLength = 20;
        const displayName = productName.length > maxNameLength
          ? productName.substring(0, maxNameLength - 3) + '...'
          : productName;

        receipt += `${displayName.padEnd(20)} ${String(quantity).padStart(3)} ${this.formatCurrency(total).padStart(8)}\n`;

        // Show unit price if different from total
        if (quantity > 1) {
          receipt += `  @ ${this.formatCurrency(price)} per item\n`;
        }
      });
    } else {
      receipt += 'Tidak ada item\n';
    }

    receipt += this.createLine(32) + '\n';

    // Totals with proper alignment
    const subtotal = validTransaction.subtotal;
    const tax = validTransaction.tax;
    const total = validTransaction.total;
    const amountPaid = validTransaction.amount_paid;
    const change = validTransaction.change_amount;

    receipt += `Subtotal:${this.formatCurrency(subtotal).padStart(23)}\n`;
    if (tax > 0) {
      receipt += `Pajak:${this.formatCurrency(tax).padStart(26)}\n`;
    }
    receipt += `${'TOTAL:'.padEnd(8)}${this.formatCurrency(total).padStart(24)}\n`;
    receipt += `Dibayar:${this.formatCurrency(amountPaid).padStart(24)}\n`;
    if (change > 0) {
      receipt += `Kembalian:${this.formatCurrency(change).padStart(22)}\n`;
    }

    receipt += this.createLine(32) + '\n';

    // Footer
    receipt += this.centerText('Terima kasih atas kunjungan Anda!', 32) + '\n';
    receipt += this.centerText('Silahkan berkunjung kembali', 32) + '\n';

    // Additional footer info if available
    if (validTransaction.store_website) {
      receipt += this.centerText(this.sanitizeText(validTransaction.store_website), 32) + '\n';
    }

    receipt += '\n\n\n';

    return receipt;
  }

  // Format currency for receipt display (thermal printer compatible)
  private formatCurrency(amount: number): string {
    // Format number secara manual untuk menghindari masalah encoding
    const numStr = Math.round(amount).toString();

    // Tambahkan pemisah ribuan secara manual
    const parts = [];
    for (let i = numStr.length; i > 0; i -= 3) {
      const start = Math.max(0, i - 3);
      parts.unshift(numStr.slice(start, i));
    }

    // Gunakan "Rp " dengan spasi untuk kompatibilitas maksimal
    return `Rp ${parts.join('.')}`;
  }

  // Format currency untuk display yang lebih lengkap (jika diperlukan)
  private formatCurrencyFull(amount: number): string {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(amount);
  }

  // Sanitize text for thermal printer compatibility
  private sanitizeText(text: string): string {
    if (!text) return '';

    // Hanya izinkan karakter ASCII dasar untuk printer thermal
    return text
      .replace(/[^\x20-\x7E]/g, '') // Hanya ASCII printable characters (32-126)
      .replace(/[áàâäãåāăąćčçďđéèêëēėęěğģħíìîïīįıķĺļľłńňñņóòôöõøōőœŕřśšşťţúùûüūůűųýÿźžż]/gi, '') // Remove accented characters
      .replace(/[₹₨₩¥€£$¢]/g, '') // Remove all currency symbols
      .replace(/[""'']/g, '"') // Replace smart quotes with regular quotes
      .replace(/[–—]/g, '-') // Replace em/en dashes with regular dash
      .replace(/\s+/g, ' ') // Replace multiple spaces with single space
      .trim();
  }

  // Create a line of dashes
  private createLine(length: number): string {
    return '-'.repeat(length);
  }

  // Center text within a given width
  private centerText(text: string, width: number): string {
    if (text.length >= width) return text;

    const padding = width - text.length;
    const leftPadding = Math.floor(padding / 2);
    const rightPadding = padding - leftPadding;

    return ' '.repeat(leftPadding) + text + ' '.repeat(rightPadding);
  }

  // Get payment status text
  private getPaymentStatusText(status: string): string {
    switch (status) {
      case 'paid':
        return 'LUNAS';
      case 'pending':
        return 'MENUNGGU';
      case 'cancelled':
        return 'DIBATALKAN';
      default:
        return 'TIDAK DIKETAHUI';
    }
  }

  // Tambahkan metode formatDate jika belum ada
  private formatDate(dateString: string): string {
    try {
      const date = new Date(dateString);
      return date.toLocaleString('id-ID', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch (error) {
      return dateString; // Return original string if formatting fails
    }
  }

  // Add QR code for digital receipt (if printer supports it)
  public addQRCode(data: string): string {
    // ESC/POS QR Code command
    // This is a basic implementation - actual QR code generation depends on printer model
    let qrCommand = '';

    // Set QR code model
    qrCommand += '\x1D\x28\x6B\x04\x00\x31\x41\x32\x00'; // Model 2

    // Set QR code size
    qrCommand += '\x1D\x28\x6B\x03\x00\x31\x43\x03'; // Size 3

    // Set error correction level
    qrCommand += '\x1D\x28\x6B\x03\x00\x31\x45\x30'; // Level L

    // Store data
    const dataLength = data.length + 3;
    const pL = dataLength & 0xFF;
    const pH = (dataLength >> 8) & 0xFF;
    qrCommand += `\x1D\x28\x6B${String.fromCharCode(pL)}${String.fromCharCode(pH)}\x31\x50\x30${data}`;

    // Print QR code
    qrCommand += '\x1D\x28\x6B\x03\x00\x31\x51\x30';

    return qrCommand;
  }

  // Generate a simple receipt summary for QR code
  public generateReceiptSummary(transaction: any): string {
    const validTransaction = this.validateTransaction(transaction);
    return `ID:${validTransaction.id}|Total:${validTransaction.total}|Date:${validTransaction.date}`;
  }

  // Format receipt sederhana untuk mengatasi masalah konten tidak lengkap
  public formatSimpleReceipt(transaction: any): string {
    const validTransaction = this.validateTransaction(transaction);
    let receipt = '';

    // Header sederhana
    receipt += '\x1B\x40'; // Initialize printer
    receipt += '\x1B\x61\x01'; // Center align
    receipt += `${this.sanitizeText(validTransaction.store_name)}\n`;
    receipt += `${this.sanitizeText(validTransaction.branch_name)}\n`;
    receipt += `${this.formatDate(validTransaction.date)}\n`;
    receipt += '================================\n';

    // Transaction info
    receipt += '\x1B\x61\x00'; // Left align
    receipt += `No: ${this.sanitizeText(validTransaction.id)}\n`;
    receipt += `Pelanggan: ${this.sanitizeText(validTransaction.customer_name)}\n`;
    receipt += `Status: ${this.getPaymentStatusText(validTransaction.payment_status)}\n`;
    receipt += '================================\n';

    // Items
    if (validTransaction.items.length > 0) {
      validTransaction.items.forEach((item: any) => {
        const name = this.sanitizeText(item.product_name);
        const displayName = name.length > 15
          ? name.substring(0, 15) + '...'
          : name;
        receipt += `${displayName}\n`;

        // Format currency dengan method yang lebih aman
        const priceText = this.formatCurrencySimple(item.price);
        const totalText = this.formatCurrencySimple(item.quantity * item.price);
        receipt += `${item.quantity} x ${priceText} = ${totalText}\n`;
      });
    }
    receipt += '================================\n';

    // Totals - gunakan format currency yang aman
    receipt += `Subtotal: ${this.formatCurrencySimple(validTransaction.subtotal)}\n`;
    if (validTransaction.tax > 0) {
      receipt += `Pajak: ${this.formatCurrencySimple(validTransaction.tax)}\n`;
    }
    receipt += `TOTAL: ${this.formatCurrencySimple(validTransaction.total)}\n`;
    receipt += `Dibayar: ${this.formatCurrencySimple(validTransaction.amount_paid)}\n`;
    if (validTransaction.change_amount > 0) {
      receipt += `Kembalian: ${this.formatCurrencySimple(validTransaction.change_amount)}\n`;
    }
    receipt += `Metode: ${this.sanitizeText(validTransaction.payment_method)}\n`;
    receipt += '================================\n';

    // Footer
    receipt += '\x1B\x61\x01'; // Center align
    receipt += 'Terima kasih!\n';
    receipt += '\n\n\n';

    // Cut paper
    receipt += '\x1D\x56\x41\x10';

    return receipt;
  }

  // Method untuk print dengan fallback ke format sederhana
  public async printWithFallback(transaction: any): Promise<boolean> {
    console.log('BluetoothPrinter: Attempting to print with fallback...');

    // Coba format sederhana terlebih dahulu (lebih stabil)
    try {
      const simpleReceipt = this.formatSimpleReceipt(transaction);
      console.log('BluetoothPrinter: Trying simple format receipt...');
      console.log('Simple receipt preview:', simpleReceipt.substring(0, 200) + '...');
      const success = await this.print(simpleReceipt);
      if (success) {
        console.log('BluetoothPrinter: Simple format printed successfully');
        return true;
      }
    } catch (error) {
      console.warn('BluetoothPrinter: Simple format failed:', error);
    }

    // Jika gagal, coba format plain
    try {
      const plainReceipt = this.formatPlainReceipt(transaction);
      console.log('BluetoothPrinter: Trying plain format receipt...');
      const success = await this.print(plainReceipt);
      if (success) {
        console.log('BluetoothPrinter: Plain format printed successfully');
        return true;
      }
    } catch (error) {
      console.warn('BluetoothPrinter: Plain format failed:', error);
    }

    // Terakhir, coba format lengkap
    try {
      const fullReceipt = this.formatReceipt(transaction);
      console.log('BluetoothPrinter: Trying full format receipt...');
      const success = await this.print(fullReceipt);
      if (success) {
        console.log('BluetoothPrinter: Full format printed successfully');
        return true;
      }
    } catch (error) {
      console.warn('BluetoothPrinter: Full format failed:', error);
    }

    console.error('BluetoothPrinter: All format attempts failed');
    return false;
  }

  // Format currency yang sangat sederhana untuk printer thermal
  private formatCurrencySimple(amount: number): string {
    // Konversi ke integer dan format manual tanpa library
    const num = Math.round(amount);
    const str = num.toString();

    // Tambahkan titik pemisah ribuan secara manual
    let result = '';
    for (let i = 0; i < str.length; i++) {
      if (i > 0 && (str.length - i) % 3 === 0) {
        result += '.';
      }
      result += str[i];
    }

    return `Rp ${result}`;
  }

  // Test method untuk melihat format currency
  public testCurrencyFormat(amount: number): void {
    console.log('=== Currency Format Test ===');
    console.log('Amount:', amount);
    console.log('formatCurrency:', this.formatCurrency(amount));
    console.log('formatCurrencySimple:', this.formatCurrencySimple(amount));
    console.log('formatCurrencyFull:', this.formatCurrencyFull(amount));
    console.log('After sanitize formatCurrency:', this.sanitizeText(this.formatCurrency(amount)));
    console.log('After sanitize formatCurrencySimple:', this.sanitizeText(this.formatCurrencySimple(amount)));

    // Test individual characters
    const currencyText = this.formatCurrency(amount);
    console.log('Character codes in formatCurrency:');
    for (let i = 0; i < currencyText.length; i++) {
      const char = currencyText[i];
      const code = char.charCodeAt(0);
      console.log(`  "${char}" = ${code} (${code > 127 ? 'NON-ASCII' : 'ASCII'})`);
    }
    console.log('===========================');
  }
}

export default new BluetoothPrinterClass();










