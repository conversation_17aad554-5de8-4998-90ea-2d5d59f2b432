-- Migration: Add branch_id column to products table for branch-level access control
-- Date: 2025-07-23
-- Description: This migration adds branch_id column to products table and creates proper foreign key relationships

-- Add branch_id column to products table
ALTER TABLE `products` ADD COLUMN `branch_id` int(11) DEFAULT NULL AFTER `user_id`;

-- Add foreign key constraints
ALTER TABLE `products` 
ADD KEY `fk_products_user` (`user_id`),
ADD KEY `fk_products_branch` (`branch_id`),
ADD CONSTRAINT `fk_products_user` FOREIGN KEY (`user_id`) REFERENCES `msusers` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
ADD CONSTRAINT `fk_products_branch` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- Note: Existing products will have branch_id = NULL, which means they belong to admin users
-- Branch users will create products with their specific branch_id
