import React, { createContext, useContext, useState, useEffect } from 'react';
import { CartItem, MenuItem, PaymentInfo } from '../types';
import config from '../config';
import { fetchWithSession, getCurrentUser } from '../utils/api';

interface CartContextType {
  cartItems: CartItem[];
  addToCart: (menuItem: MenuItem) => void;
  removeFromCart: (itemId: string) => void;
  updateQuantity: (itemId: string, quantity: number) => void;
  clearCart: () => void;
  paymentInfo: PaymentInfo;
  updatePaymentAmount: (amount: number) => void;
  getSubtotal: () => number;
  customerName: string;
  updateCustomerName: (name: string) => void;
  memberId: number | null;
  updateMemberId: (id: number | null) => void;
  checkout: (customerName: string, paymentInfo: PaymentInfo) => Promise<void>;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

export const CartProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [customerName, setCustomerName] = useState<string>('Customer');
  const [memberId, setMemberId] = useState<number | null>(null);
  const [taxPercentage, setTaxPercentage] = useState<number>(0);
  const [paymentInfo, setPaymentInfo] = useState<PaymentInfo>({
    subtotal: 0,
    tax: 0,
    total: 0,
    amountPaid: 0,
    change: 0,
  });

  // Fungsi untuk mengambil konfigurasi pajak dari store
  const fetchTaxConfiguration = async () => {
    try {
      const currentUser = getCurrentUser();
      const userId = currentUser?.id;

      if (!userId) return;

      const response = await fetchWithSession(`${config.apiUrl}/store-config?user_id=${userId}`);
      if (response && response.ok) {
        const data = await response.json();
        setTaxPercentage(data.tax_percentage || 0);
      }
    } catch (error) {
      console.error('Error fetching tax configuration:', error);
      setTaxPercentage(0);
    }
  };

  // Load tax configuration saat component mount
  useEffect(() => {
    fetchTaxConfiguration();
  }, []);

  const getSubtotal = (): number => {
    return cartItems.reduce((sum, item) => sum + (item.menuItem?.price || item.price || 0) * item.quantity, 0);
  };

  const calculateTotals = (amountPaid = paymentInfo.amountPaid) => {
    const subtotal = getSubtotal();
    // Hitung pajak berdasarkan persentase dari store config
    const tax = subtotal * (taxPercentage / 100);
    const total = subtotal + tax;
    const change = amountPaid - total > 0 ? amountPaid - total : 0;

    setPaymentInfo({
      subtotal,
      tax,
      total,
      amountPaid,
      change,
    });
  };

  const addToCart = (item: MenuItem) => {
    setCartItems((prevItems: CartItem[]): CartItem[] => {
      const existingItem = prevItems.find(cartItem => cartItem.menuItem?.id === item.id);

      if (existingItem) {
        // If item already exists, increase quantity
        return prevItems.map(cartItem =>
          cartItem.menuItem?.id === item.id
            ? { ...cartItem, quantity: cartItem.quantity + 1 }
            : cartItem
        );
      } else {
        // If item doesn't exist, add it with quantity 1
        return [...prevItems, { id: item.id, menuItem: item, quantity: 1 }];
      }
    });

    // Update totals after cart change
    setTimeout(() => calculateTotals(), 0);
  };

  const removeFromCart = (itemId: string) => {
    setCartItems(prevItems => prevItems.filter(item => item.menuItem?.id !== itemId));

    // Update totals after cart change
    setTimeout(() => calculateTotals(), 0);
  };

  const updateQuantity = (itemId: string, quantity: number) => {
    if (quantity <= 0) {
      removeFromCart(itemId);
      return;
    }

    setCartItems(prevItems =>
      prevItems.map(item =>
        item.menuItem?.id === itemId
          ? { ...item, quantity }
          : item
      )
    );

    // Update totals after cart change
    setTimeout(() => calculateTotals(), 0);
  };

  const clearCart = () => {
    // Simpan transaksi ke sessionStorage sebelum menghapus cart
    if (cartItems.length > 0) {
      // Hitung ulang total untuk memastikan nilai yang benar
      const calculatedSubtotal = getSubtotal();
      const calculatedTax = calculatedSubtotal * (taxPercentage / 100);
      const calculatedTotal = calculatedSubtotal + calculatedTax;

      // Ambil informasi user yang sedang login
      const currentUser = JSON.parse(sessionStorage.getItem('currentUser') || '{}');

      const transaction = {
        id: `TRX-${Date.now()}`,
        date: new Date().toISOString(),
        customerName: customerName,
        items: cartItems.map(item => ({
          name: item.menuItem?.name,
          price: item.menuItem?.price,
          quantity: item.quantity
        })),
        subtotal: calculatedSubtotal,
        tax: calculatedTax,
        total: calculatedTotal,
        amountPaid: paymentInfo.amountPaid,
        change: paymentInfo.change,
        paymentMethod: 'Cash',
        // Tambahkan informasi cabang
        branchId: currentUser.id,
        branchName: currentUser.name
      };

      // Ambil transaksi yang sudah ada
      const storedTransactions = sessionStorage.getItem('bakeryTransactions');
      const transactions = storedTransactions ? JSON.parse(storedTransactions) : [];

      // Tambahkan transaksi baru
      transactions.push(transaction);

      // Simpan kembali ke sessionStorage
      sessionStorage.setItem('bakeryTransactions', JSON.stringify(transactions));
    }

    // Bersihkan cart
    setCartItems([]);
    setPaymentInfo({
      subtotal: 0,
      tax: 0,
      total: 0,
      amountPaid: 0,
      change: 0,
    });
    // Reset customerName ke default
    setCustomerName('Customer');
  };

  const updatePaymentAmount = (amount: number) => {
    // Pastikan amount adalah angka valid
    const validAmount = isNaN(amount) ? 0 : amount;
    calculateTotals(validAmount);
  };

  // Tambahkan fungsi untuk memperbarui nama pelanggan
  const updateCustomerName = (name: string) => {
    setCustomerName(name);
  };

  // Tambahkan fungsi untuk memperbarui memberId
  const updateMemberId = (id: number | null) => {
    setMemberId(id);
  };

  const checkout = async (customerName: string, paymentAmount: number) => {
    if (cartItems.length > 0) {
      const subtotal = getSubtotal();
      const tax = subtotal * (taxPercentage / 100);
      const total = subtotal + tax;
      const change = paymentAmount - total;

      if (paymentAmount < total) {
        alert('Pembayaran kurang dari total');
        return;
      }

      // Buat objek transaksi
      const transaction = {
        customer_name: customerName,
        subtotal: subtotal,
        tax: tax,
        total_amount: total,
        payment_amount: paymentAmount,
        change_amount: change,
        branch_id: 1, // Sesuaikan dengan ID cabang yang sesuai
        items: cartItems.map(item => ({
          id: item.id, // ID produk
          product_id: item.id, // Tambahkan ID produk
          product_name: item.name,
          price: item.price,
          quantity: item.quantity
        }))
      };

      try {
        // Kirim transaksi ke API
        const response = await fetch(`${config.apiUrl}/transactions`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(transaction)
        });

        if (!response.ok) {
          alert('Gagal menyimpan transaksi ke database');
          return;
        }
      } catch (error) {
        alert('Gagal menyimpan transaksi ke database');
        return;
      }

      // Bersihkan cart
      setCartItems([]);
      setPaymentInfo({
        subtotal: 0,
        tax: 0,
        total: 0,
        amountPaid: 0,
        change: 0,
      });
      // Reset customerName ke default
      setCustomerName('Customer');
    }
  };

  return (
    <CartContext.Provider
      value={{
        cartItems,
        addToCart,
        removeFromCart,
        updateQuantity,
        clearCart,
        paymentInfo,
        updatePaymentAmount,
        getSubtotal,
        customerName,
        updateCustomerName,
        memberId,
        updateMemberId,
        checkout: (customerName: string, paymentInfo: PaymentInfo) => checkout(customerName, paymentInfo.amountPaid),
      }}
    >
      {children}
    </CartContext.Provider>
  );
};

export const useCart = () => {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};



















