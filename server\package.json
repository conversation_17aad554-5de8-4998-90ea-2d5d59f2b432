{"name": "bakery-server", "version": "1.0.0", "description": "Backend server for bakery management system", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js"}, "dependencies": {"axios": "^1.9.0", "body-parser": "^1.20.2", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.3.1", "express": "^4.18.2", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.1"}, "devDependencies": {"nodemon": "^3.0.1"}}