import React, { useState, useEffect } from 'react';
import { Trash2, Printer, Bluetooth, CheckCircle, AlertCircle, ShoppingBag, Minus, Plus } from 'lucide-react';
import { useCart } from '../context/CartContext';
import BluetoothPrinter from '../utils/BluetoothPrinter';
import { getCurrentUser, fetchWithSession } from '../utils/api';
import config from '../config';

// Tambahkan interface untuk StoreConfig
interface StoreConfig {
  store_name: string;
  // properti lain bisa ditambahkan sesuai kebutuhan
}

const Cart: React.FC = () => {
  const {
    cartItems,
    removeFromCart,
    updateQuantity,
    paymentInfo,
    updatePaymentAmount,
    clearCart,
    customerName
  } = useCart();

  const [paymentInput, setPaymentInput] = useState<string>('');
  const [showReceipt, setShowReceipt] = useState<boolean>(false);
  const [paymentFeedback, setPaymentFeedback] = useState<string>('');
  const [showPaymentAnimation, setShowPaymentAnimation] = useState<boolean>(false);
  const [isPrinterConnected, setIsPrinterConnected] = useState<boolean>(false);
  const [isPrinting, setIsPrinting] = useState<boolean>(false);
  const [storeConfig, setStoreConfig] = useState<StoreConfig>({ store_name: 'Satu Lisan' });

  // Fungsi untuk mengambil konfigurasi toko
  const fetchStoreConfig = async () => {
    try {
      const currentUser = getCurrentUser();
      const userId = currentUser?.id;

      if (!userId) return;

      const response = await fetchWithSession(`${config.apiUrl}/store-config?user_id=${userId}`);
      if (response && response.ok) {
        const data = await response.json();
        setStoreConfig(data);
      }
    } catch (error) {
      // Handle error silently
    }
  };

  useEffect(() => {
    if (paymentInfo.amountPaid > 0) {
      if (paymentInfo.amountPaid < paymentInfo.total) {
        setPaymentFeedback('Pembayaran kurang');
      } else {
        setPaymentFeedback('Pembayaran cukup');
        setShowPaymentAnimation(true);
        setTimeout(() => setShowPaymentAnimation(false), 1500);
      }
    } else {
      setPaymentFeedback('');
    }
    fetchStoreConfig(); // Tambahkan ini
  }, [paymentInfo.amountPaid, paymentInfo.total]);

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(price);
  };

  const handlePaymentChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setPaymentInput(value);
    updatePaymentAmount(value ? parseInt(value) : 0);
  };

  // Fungsi untuk menghubungkan ke printer Bluetooth
  const connectToPrinter = async () => {
    try {
      if (!BluetoothPrinter.isSupported()) {
        alert('Bluetooth tidak didukung di perangkat ini');
        return;
      }

      const connected = await BluetoothPrinter.connect();
      setIsPrinterConnected(connected);

      if (connected) {
        alert('Berhasil terhubung ke printer Bluetooth');
      } else {
        alert('Gagal terhubung ke printer Bluetooth');
      }
    } catch (error) {
      alert('Gagal terhubung ke printer: ' + (error instanceof Error ? error.message : 'Unknown error'));
    }
  };

  // Fungsi untuk mencetak ke printer Bluetooth
  const printToBluetoothPrinter = async () => {
    if (cartItems.length === 0) return;

    try {
      setIsPrinting(true);

      if (!isPrinterConnected) {
        const connected = await BluetoothPrinter.connect();
        setIsPrinterConnected(connected);

        if (!connected) {
          alert('Gagal terhubung ke printer Bluetooth. Pastikan printer dinyalakan dan dalam mode pairing.');
          setIsPrinting(false);
          return;
        }
      }

      // Gunakan tanggal dengan timezone Asia/Jakarta
      const now = new Date();
      const jakartaTime = new Intl.DateTimeFormat('en-US', {
        timeZone: 'Asia/Jakarta',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      }).format(now);

      // Konversi format MM/DD/YYYY, HH:MM:SS ke YYYY-MM-DD HH:MM:SS
      const [datePart, timePart] = jakartaTime.split(', ');
      const [month, day, year] = datePart.split('/');
      const formattedDate = `${year}-${month}-${day} ${timePart}`;

      // Dapatkan user yang sedang login
      const currentUser = getCurrentUser();
      const outletId = currentUser?.role === 'admin' ? currentUser?.id : null;

      const transaction = {
        id: `TRX-${Date.now()}`,
        date: formattedDate,
        customer_name: customerName,
        branch_name: 'Cabang Utama',
        outlet_id: outletId, // Tambahkan outlet_id
        subtotal: paymentInfo.subtotal,
        tax: paymentInfo.tax,
        total: paymentInfo.total,
        amount_paid: paymentInfo.amountPaid,
        change_amount: paymentInfo.change,
        payment_method: 'Cash',
        items: cartItems.map(item => ({
          product_id: item.menuItem?.id, // Tambahkan ID produk
          product_name: item.menuItem?.name || 'Unknown',
          price: item.menuItem?.price || 0,
          quantity: item.quantity
        })),
        store_name: storeConfig.store_name, // Tambahkan ini
      };

      // Gunakan method printWithFallback yang mencoba beberapa format
      const success = await BluetoothPrinter.printWithFallback(transaction);

      if (success) {
        alert('Struk berhasil dicetak');
        // Bersihkan cart setelah berhasil cetak
        clearCart();
      } else {
        alert('Gagal mencetak struk. Pastikan printer terhubung dengan benar dan dalam keadaan siap. Periksa console untuk detail error.');
      }
    } catch (error) {
      alert('Gagal mencetak: ' + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setIsPrinting(false);
    }
  };

  // Modifikasi handlePrintReceipt untuk memberikan opsi cetak
  const handlePrintReceipt = () => {
    if (window.confirm('Pilih metode cetak:\nOK - Printer Bluetooth\nCancel - Printer Sistem')) {
      // Cetak ke printer Bluetooth
      printToBluetoothPrinter();
    } else {
      // Cetak menggunakan metode browser default
      setShowReceipt(true);
      setTimeout(() => {
        window.print();
        // Bersihkan cart setelah berhasil cetak
        clearCart();
      }, 100);
    }
  };

  const handleQuantityChange = (itemId: string, newQuantity: number) => {
    if (newQuantity < 1) {
      removeFromCart(itemId);
    } else {
      updateQuantity(itemId, newQuantity);
    }
  };

  const receiptDate = new Date().toLocaleString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    timeZone: 'Asia/Jakarta'
  });

  return (
    <div id="cart" className="receipt-section">
      <h2 className="receipt-heading flex items-center gap-2">
        <ShoppingBag size={20} className="text-accent-500" />
        Pesanan Anda
      </h2>

      {cartItems.length === 0 ? (
        <div className="empty-state">
          <ShoppingBag size={48} className="text-neutral-300 mb-3" />
          <p className="font-medium">Keranjang Anda kosong</p>
          <p className="text-sm mt-1">Silakan tambahkan menu untuk melakukan pemesanan</p>
        </div>
      ) : (
        <>
          <div className="mb-4 max-h-[300px] overflow-y-auto pr-1 space-y-1">
            {cartItems.map((item, index) => (
              <div
                key={item.menuItem?.id}
                className="cart-item animate-fade-in"
                style={{ animationDelay: `${index * 0.05}s` }}
              >
                <div className="flex-1">
                  <h3 className="font-medium text-primary-800">{item.menuItem?.name}</h3>
                  <p className="text-sm text-neutral-600">{formatPrice(item.menuItem?.price || 0)}</p>
                </div>
                <div className="flex items-center gap-1">
                  <button
                    onClick={() => item.menuItem?.id && handleQuantityChange(item.menuItem.id, item.quantity - 1)}
                    className="cart-qty-btn"
                    aria-label="Kurangi jumlah"
                  >
                    <Minus size={16} />
                  </button>
                  <span className="w-8 text-center font-medium">{item.quantity}</span>
                  <button
                    onClick={() => item.menuItem?.id && updateQuantity(item.menuItem.id, item.quantity + 1)}
                    className="cart-qty-btn"
                    aria-label="Tambah jumlah"
                  >
                    <Plus size={16} />
                  </button>
                  <button
                    onClick={() => item.menuItem?.id && removeFromCart(item.menuItem.id)}
                    className="cart-remove-btn ml-1"
                    aria-label={`Hapus ${item.menuItem?.name}`}
                  >
                    <Trash2 size={16} />
                  </button>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-5 space-y-2 bg-neutral-50 p-4 rounded-lg">
            <div className="flex justify-between text-sm">
              <span className="text-neutral-600">Subtotal:</span>
              <span className="font-medium">{formatPrice(paymentInfo.subtotal)}</span>
            </div>
            {paymentInfo.tax > 0 && (
              <div className="flex justify-between text-sm">
                <span className="text-neutral-600">Pajak:</span>
                <span className="font-medium">{formatPrice(paymentInfo.tax)}</span>
              </div>
            )}
            <div className="flex justify-between font-bold text-lg border-t border-neutral-200 pt-2 mt-2">
              <span>Total:</span>
              <span className="text-primary-800">{formatPrice(paymentInfo.total)}</span>
            </div>

            <div className="mt-5 pt-4">
              <label className="block text-sm font-medium text-primary-700 mb-1">
                Jumlah Pembayaran:
              </label>
              <input
                type="number"
                value={paymentInput}
                onChange={handlePaymentChange}
                className="input-field"
                placeholder="Masukkan jumlah pembayaran"
                autoComplete="off"
              />
            </div>

            {paymentInfo.amountPaid > 0 && (
              <div className={`mt-3 p-3 rounded-md ${paymentInfo.change >= 0
                ? "bg-green-50 border border-green-100"
                : "bg-red-50 border border-red-100"
                } ${showPaymentAnimation ? 'animate-pulse-once' : ''}`}>
                <div className="flex items-start gap-2">
                  {paymentInfo.change >= 0 ? (
                    <CheckCircle size={18} className="text-success-500 mt-0.5" />
                  ) : (
                    <AlertCircle size={18} className="text-error-500 mt-0.5" />
                  )}
                  <div>
                    <div className="font-medium text-sm mb-1">
                      {paymentInfo.change >= 0 ? paymentFeedback : paymentFeedback}
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Kembalian:</span>
                      <span className={`font-bold ${paymentInfo.change >= 0 ? "text-success-500" : "text-error-500"
                        }`}>
                        {formatPrice(paymentInfo.change)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div className="mt-6 flex gap-2">
              {!isPrinterConnected && (
                <button
                  onClick={connectToPrinter}
                  className="flex items-center justify-center gap-2 px-4 py-2 rounded-lg bg-neutral-100 text-neutral-700 hover:bg-neutral-200 transition-all duration-300"
                >
                  <Bluetooth size={18} />
                </button>
              )}
              <button
                onClick={handlePrintReceipt}
                disabled={paymentInfo.amountPaid < paymentInfo.total || isPrinting}
                className={`flex-1 flex items-center justify-center gap-2 px-4 py-2 rounded-lg text-white font-medium transition-all duration-300 ${paymentInfo.amountPaid >= paymentInfo.total && !isPrinting
                  ? "bg-primary-600 hover:bg-primary-700 shadow-md hover:shadow-lg"
                  : "bg-neutral-400 cursor-not-allowed"
                  }`}
              >
                {isPrinting ? (
                  <>
                    <span className="animate-spin mr-1">⏳</span>
                    <span>Mencetak...</span>
                  </>
                ) : (
                  <>
                    <Printer size={18} />
                    <span>Cetak Struk</span>
                  </>
                )}
              </button>
              <button
                onClick={clearCart}
                className="px-4 py-2 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors font-medium flex-shrink-0"
                aria-label="Batalkan pesanan"
              >
                <Trash2 size={18} />
              </button>
            </div>
          </div>
        </>
      )}

      {/* Print-only receipt */}
      {showReceipt && (
        <div className="hidden print:block p-4">
          <div className="text-center mb-6">
            <h1 className="text-2xl font-bold font-display">Restoran Nusantara</h1>
            <p className="text-sm">Jl. Raya No. 123, Jakarta</p>
            <p className="text-sm">Telp: 021-12345678</p>
            <div className="border-t border-b border-dashed my-3 py-1">
              <p className="text-sm">{receiptDate}</p>
              <p className="text-xs">No. Order: #RN{Math.floor(Math.random() * 10000)}</p>
              <p className="text-xs">Pelanggan: {customerName}</p>
            </div>
          </div>

          <div className="mb-6">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b border-neutral-300">
                  <th className="text-left py-2">Item</th>
                  <th className="text-center py-2">Qty</th>
                  <th className="text-right py-2">Harga</th>
                  <th className="text-right py-2">Total</th>
                </tr>
              </thead>
              <tbody>
                {cartItems.map((item) => (
                  <tr key={item.menuItem?.id} className="border-b border-dotted border-neutral-200">
                    <td className="py-2">{item.menuItem?.name}</td>
                    <td className="text-center py-2">{item.quantity}x</td>
                    <td className="text-right py-2">{formatPrice(item.menuItem?.price || 0)}</td>
                    <td className="text-right py-2">{formatPrice((item.menuItem?.price || 0) * item.quantity)}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          <div className="text-sm">
            <div className="flex justify-between py-1">
              <span>Subtotal:</span>
              <span>{formatPrice(paymentInfo.subtotal)}</span>
            </div>
            {paymentInfo.tax > 0 && (
              <div className="flex justify-between py-1">
                <span>Pajak:</span>
                <span>{formatPrice(paymentInfo.tax)}</span>
              </div>
            )}
            <div className="flex justify-between font-bold border-t border-b py-2 my-2">
              <span>Total:</span>
              <span>{formatPrice(paymentInfo.total)}</span>
            </div>
            <div className="flex justify-between py-1">
              <span>Dibayar:</span>
              <span>{formatPrice(paymentInfo.amountPaid)}</span>
            </div>
            <div className="flex justify-between font-bold py-1">
              <span>Kembalian:</span>
              <span>{formatPrice(paymentInfo.change)}</span>
            </div>
          </div>

          <div className="text-center mt-8 pt-2 border-t border-dashed">
            <p className="font-medium">Terima Kasih Atas Kunjungan Anda</p>
            <p className="text-xs mt-1">Silahkan berkunjung kembali</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default Cart;







