import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { ArrowLeft, Save, AlertTriangle, Check } from 'lucide-react';
import config from '../config';
import { fetchWithSession, getCurrentUser } from '../utils/api';

interface PaymentGateway {
  id: number;
  provider: string;
  is_production: number;
  client_key: string;
  server_key: string;
  is_active: number;
  user_id: number;
}

const PaymentGatewayConfig: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [showSuccessAlert, setShowSuccessAlert] = useState(false);
  const [midtransConfig, setMidtransConfig] = useState<PaymentGateway>({
    id: 0,
    provider: 'midtrans',
    is_production: 0,
    client_key: '',
    server_key: '',
    is_active: 0,
    user_id: 0
  });

  useEffect(() => {
    fetchPaymentGateways();
  }, []);

  const fetchPaymentGateways = async () => {
    setIsLoading(true);
    try {
      // Dapatkan user_id dari user yang sedang login
      const currentUser = getCurrentUser();
      const userId = currentUser?.id;

      const response = await fetchWithSession(`${config.apiUrl}/payment-gateways?user_id=${userId}`);
      if (response && response.ok) {
        const data = await response.json();
        // Cari konfigurasi Midtrans
        const midtrans = data.find((gateway: PaymentGateway) => gateway.provider === 'midtrans');
        if (midtrans) {
          setMidtransConfig(midtrans);
        } else {
          // Jika tidak ada konfigurasi, set user_id
          setMidtransConfig(prev => ({
            ...prev,
            user_id: userId
          }));
        }
      }
    } catch (error) { } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;

    setMidtransConfig(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (checked ? 1 : 0) : value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);

    try {
      let response;

      if (midtransConfig.id) {
        // Update existing config
        response = await fetchWithSession(`${config.apiUrl}/payment-gateways/${midtransConfig.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(midtransConfig)
        });
      } else {
        // Create new config
        response = await fetchWithSession(`${config.apiUrl}/payment-gateways`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(midtransConfig)
        });
      }

      if (response && response.ok) {
        const updatedConfig = await response.json();
        setMidtransConfig(updatedConfig);
        setShowSuccessAlert(true);

        // Hide success alert after 3 seconds
        setTimeout(() => {
          setShowSuccessAlert(false);
        }, 3000);
      } else {
        const errorData = await response?.json();
        alert(errorData?.error || 'Gagal menyimpan konfigurasi');
      }
    } catch (error) {
      alert('Terjadi kesalahan saat menyimpan konfigurasi');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="container mx-auto p-4">
      {/* Header */}
      <div className="flex items-center mb-6">
        <Link to="/" className="p-2 rounded-full hover:bg-neutral-100">
          <ArrowLeft size={20} />
        </Link>
        <h1 className="text-2xl font-bold text-primary-800 ml-2">Konfigurasi Payment Gateway</h1>
      </div>

      {/* Success Alert */}
      {showSuccessAlert && (
        <div className="bg-green-100 border border-green-200 text-green-800 rounded-lg p-4 mb-6 flex items-center">
          <Check size={20} className="mr-2" />
          <span>Konfigurasi berhasil disimpan!</span>
        </div>
      )}

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
        </div>
      ) : (
        <div className="bg-white rounded-xl shadow-sm overflow-hidden">
          <div className="p-6">
            <h2 className="text-lg font-medium text-neutral-800 mb-4">Midtrans Payment Gateway</h2>

            <form onSubmit={handleSubmit} autoComplete="off">
              <div className="mb-4">
                <div className="flex items-center mb-4">
                  <input
                    type="checkbox"
                    id="is_active"
                    name="is_active"
                    checked={midtransConfig.is_active === 1}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-neutral-300 rounded"
                  />
                  <label htmlFor="is_active" className="ml-2 block text-sm text-neutral-700">
                    Aktifkan Midtrans Payment Gateway
                  </label>
                </div>

                <div className="flex items-center mb-6">
                  <input
                    type="checkbox"
                    id="is_production"
                    name="is_production"
                    checked={midtransConfig.is_production === 1}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-neutral-300 rounded"
                  />
                  <label htmlFor="is_production" className="ml-2 block text-sm text-neutral-700">
                    Mode Produksi (nonaktifkan untuk mode sandbox/testing)
                  </label>
                </div>

                <div className="mb-4">
                  <label htmlFor="client_key" className="block text-sm font-medium text-neutral-700 mb-1">
                    Client Key
                  </label>
                  <input
                    type="text"
                    id="client_key"
                    name="client_key"
                    value={midtransConfig.client_key}
                    onChange={handleInputChange}
                    className="w-full p-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="Masukkan Client Key dari Midtrans Dashboard"
                    autoComplete="off"
                  />
                  <p className="mt-1 text-sm text-neutral-500">
                    Client Key digunakan untuk integrasi frontend (Snap.js)
                  </p>
                </div>

                <div className="mb-4">
                  <label htmlFor="server_key" className="block text-sm font-medium text-neutral-700 mb-1">
                    Server Key
                  </label>
                  <input
                    type="text"
                    id="server_key"
                    name="server_key"
                    value={midtransConfig.server_key}
                    onChange={handleInputChange}
                    className="w-full p-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="Masukkan Server Key dari Midtrans Dashboard"
                    autoComplete="off"
                  />
                  <p className="mt-1 text-sm text-neutral-500">
                    Server Key digunakan untuk integrasi backend dan verifikasi pembayaran
                  </p>
                </div>

                <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
                  <div className="flex items-start">
                    <AlertTriangle size={20} className="text-yellow-600 mr-2 mt-0.5" />
                    <div>
                      <p className="text-sm text-yellow-700">
                        <strong>Penting:</strong> Dapatkan Client Key dan Server Key dari dashboard Midtrans Anda.
                      </p>
                      <p className="text-sm text-yellow-700 mt-1">
                        Untuk mode testing, gunakan kredensial Sandbox. Untuk transaksi nyata, gunakan kredensial Production.
                      </p>
                      <a
                        href="https://dashboard.midtrans.com/settings/config_info"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-primary-600 hover:underline text-sm mt-1 inline-block"
                      >
                        Buka Dashboard Midtrans
                      </a>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-end">
                <button
                  type="submit"
                  disabled={isSaving}
                  className={`flex items-center px-4 py-2 rounded-md text-white font-medium ${isSaving ? 'bg-neutral-400 cursor-not-allowed' : 'bg-primary-600 hover:bg-primary-700'
                    }`}
                >
                  {isSaving ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                      Menyimpan...
                    </>
                  ) : (
                    <>
                      <Save size={18} className="mr-2" />
                      Simpan Konfigurasi
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default PaymentGatewayConfig;

