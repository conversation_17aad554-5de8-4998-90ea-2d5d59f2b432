-- Create database if it doesn't exist
CREATE DATABASE IF NOT EXISTS satulisan;
USE satulisan;

-- Create a basic admin user for testing
INSERT IGNORE INTO msusers (id, name, email, password, role, is_active) VALUES 
(1, 'Admin', '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 1);

-- Create a basic branch user for testing  
INSERT IGNORE INTO msusers (id, name, email, password, role, is_active) VALUES 
(2, 'Cabang Test', '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'branch', 1);

-- Create a test branch
INSERT IGNORE INTO branches (id, name, address, phone, manager, is_active, email, password, user_id) VALUES 
(1, '<PERSON><PERSON>ng <PERSON>tam<PERSON>', 'Jl. Test No. 123', '081234567890', 'Manager Test', 1, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 1);

-- Create store config
INSERT IGNORE INTO store_config (id, store_name, store_address, store_phone, store_email, tax_percentage, user_id) VALUES 
(1, 'Satu Lisan POS', 'Jl. Contoh No. 123, Jakarta', '021-12345678', '<EMAIL>', 11.00, 1);
