import React, { useState, useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Menu, User, Tag, Package, History, BarChart2, BarChart3, Building, LogOut, AlertTriangle, Lock, CreditCard, Settings, ChevronDown, CreditCardIcon, Users, DollarSign } from 'lucide-react';

interface HeaderProps {
  onLogout: () => void;
  userName: string;
  userRole: 'admin' | 'branch' | 'cashier';
}

const Header: React.FC<HeaderProps> = ({ onLogout, userName, userRole }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [showLogoutConfirm, setShowLogoutConfirm] = useState(false);
  const [adminMenuOpen, setAdminMenuOpen] = useState(false);
  const [productMenuOpen, setProductMenuOpen] = useState(false);
  const [configMenuOpen, setConfigMenuOpen] = useState(false);

  const adminMenuRef = useRef<HTMLDivElement>(null);
  const productMenuRef = useRef<HTMLDivElement>(null);
  const configMenuRef = useRef<HTMLDivElement>(null);

  const isAdmin = userRole === 'admin';

  const handleLogoutClick = () => {
    setShowLogoutConfirm(true);
    setIsMenuOpen(false);
  };

  const confirmLogout = () => {
    onLogout();
    setShowLogoutConfirm(false);
  };

  const cancelLogout = () => {
    setShowLogoutConfirm(false);
  };

  // Close dropdown menus when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (adminMenuRef.current && !adminMenuRef.current.contains(event.target as Node)) {
        setAdminMenuOpen(false);
      }
      if (productMenuRef.current && !productMenuRef.current.contains(event.target as Node)) {
        setProductMenuOpen(false);
      }
      if (configMenuRef.current && !configMenuRef.current.contains(event.target as Node)) {
        setConfigMenuOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <header className="bg-white shadow-sm">
      <div className="container mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Link
              to={userRole === 'cashier' ? '/dashboard' : '/dashboard-summary'}
              className="text-xl font-bold text-primary-600 mr-8"
            >
              Satu Lisan POS
            </Link>

            {/* Main Navigation Links */}
            <nav className="hidden md:flex items-center space-x-4 lg:space-x-6">
              {/* Order link - only for cashiers */}
              {userRole === 'cashier' && (
                <Link to="/dashboard" className="flex items-center gap-2 text-neutral-700 hover:text-primary-600">
                  <Package size={18} className="text-primary-600" />
                  <span>Order</span>
                </Link>
              )}

              {/* Dashboard Summary - only for admin and branch */}
              {(userRole === 'admin' || userRole === 'branch') && (
                <Link to="/dashboard-summary" className="flex items-center gap-2 text-neutral-700 hover:text-primary-600">
                  <BarChart3 size={18} className="text-primary-600" />
                  <span>Dashboard</span>
                </Link>
              )}

              <Link to="/transactions" className="flex items-center gap-2 text-neutral-700 hover:text-primary-600">
                <History size={18} className="text-primary-600" />
                <span>Transaksi</span>
              </Link>

              {userRole !== 'cashier' && (
                <Link to="/reports" className="flex items-center gap-2 text-neutral-700 hover:text-primary-600">
                  <BarChart2 size={18} className="text-primary-600" />
                  <span>Laporan</span>
                </Link>
              )}

              {/* Expenses - only for admin and branch */}
              {(userRole === 'admin' || userRole === 'branch') && (
                <Link to="/expenses" className="flex items-center gap-2 text-neutral-700 hover:text-primary-600">
                  <DollarSign size={18} className="text-primary-600" />
                  <span>Pengeluaran</span>
                </Link>
              )}

              {/* Product Management for branch users */}
              {userRole === 'branch' && (
                <Link to="/products" className="flex items-center gap-2 text-neutral-700 hover:text-primary-600">
                  <Package size={18} className="text-primary-600" />
                  <span>Produk</span>
                </Link>
              )}

              {/* Category Management for branch users */}
              {userRole === 'branch' && (
                <Link to="/categories" className="flex items-center gap-2 text-neutral-700 hover:text-primary-600">
                  <Tag size={18} className="text-primary-600" />
                  <span>Kategori</span>
                </Link>
              )}

              {/* Add Cashier Management link for branch users */}
              {userRole === 'branch' && (
                <Link to="/cashier-management" className="flex items-center gap-2 text-neutral-700 hover:text-primary-600">
                  <Users size={18} className="text-primary-600" />
                  <span>Manajemen Kasir</span>
                </Link>
              )}

              {isAdmin && (
                <>
                  {/* Tablet view - Admin dropdown menu */}
                  <div className="relative md:block lg:hidden" ref={adminMenuRef}>
                    <button
                      onClick={() => setAdminMenuOpen(!adminMenuOpen)}
                      className="flex items-center gap-2 text-neutral-700 hover:text-primary-600 py-2 focus:outline-none"
                    >
                      <Settings size={18} className="text-primary-600" />
                      <span>Admin</span>
                      <ChevronDown size={16} className={`transition-transform ${adminMenuOpen ? 'rotate-180' : ''}`} />
                    </button>

                    {adminMenuOpen && (
                      <div className="absolute left-0 mt-1 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50">
                        <div className="py-1" role="menu" aria-orientation="vertical">
                          <Link
                            to="/products"
                            className="flex items-center gap-2 px-4 py-3 text-sm text-neutral-700 hover:bg-neutral-100 transition-colors"
                            onClick={() => setAdminMenuOpen(false)}
                          >
                            <Package size={18} className="text-primary-600" />
                            <span>Produk</span>
                          </Link>

                          <Link
                            to="/categories"
                            className="flex items-center gap-2 px-4 py-3 text-sm text-neutral-700 hover:bg-neutral-100 transition-colors"
                            onClick={() => setAdminMenuOpen(false)}
                          >
                            <Tag size={18} className="text-primary-600" />
                            <span>Kategori</span>
                          </Link>

                          <Link
                            to="/branches"
                            className="flex items-center gap-2 px-4 py-3 text-sm text-neutral-700 hover:bg-neutral-100 transition-colors"
                            onClick={() => setAdminMenuOpen(false)}
                          >
                            <Building size={18} className="text-primary-600" />
                            <span>Cabang</span>
                          </Link>

                          <Link
                            to="/payment-methods"
                            className="flex items-center gap-2 px-4 py-3 text-sm text-neutral-700 hover:bg-neutral-100 transition-colors"
                            onClick={() => setAdminMenuOpen(false)}
                          >
                            <CreditCard size={18} className="text-primary-600" />
                            <span>Metode Pembayaran</span>
                          </Link>

                          <Link
                            to="/payment-gateway-config"
                            className="flex items-center gap-2 px-4 py-3 text-sm text-neutral-700 hover:bg-neutral-100 transition-colors"
                            onClick={() => setAdminMenuOpen(false)}
                          >
                            <CreditCardIcon size={18} className="text-primary-600" />
                            <span>Payment Gateway</span>
                          </Link>

                          <Link
                            to="/store-config"
                            className="flex items-center gap-2 px-4 py-3 text-sm text-neutral-700 hover:bg-neutral-100 transition-colors"
                            onClick={() => setAdminMenuOpen(false)}
                          >
                            <Settings size={18} className="text-primary-600" />
                            <span>Konfigurasi Toko</span>
                          </Link>

                          <Link
                            to="/members"
                            className="flex items-center gap-2 px-4 py-3 text-sm text-neutral-700 hover:bg-neutral-100 transition-colors"
                            onClick={() => setAdminMenuOpen(false)}
                          >
                            <Users size={18} className="text-primary-600" />
                            <span>Member</span>
                          </Link>

                          <Link
                            to="/cashier-management"
                            className="flex items-center gap-2 px-4 py-3 text-sm text-neutral-700 hover:bg-neutral-100 transition-colors"
                            onClick={() => setAdminMenuOpen(false)}
                          >
                            <Users size={18} className="text-primary-600" />
                            <span>Manajemen Kasir</span>
                          </Link>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Desktop view - Grouped dropdown menus */}
                  <div className="hidden lg:flex items-center space-x-6">
                    {/* Produk & Kategori Dropdown */}
                    <div className="relative" ref={productMenuRef}>
                      <button
                        onClick={() => setProductMenuOpen(!productMenuOpen)}
                        className="flex items-center gap-2 text-neutral-700 hover:text-primary-600 py-2 focus:outline-none"
                      >
                        <Package size={18} className="text-primary-600" />
                        <span>Produk & Kategori</span>
                        <ChevronDown size={16} className={`transition-transform ${productMenuOpen ? 'rotate-180' : ''}`} />
                      </button>

                      {productMenuOpen && (
                        <div className="absolute left-0 mt-1 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50">
                          <div className="py-1" role="menu" aria-orientation="vertical">
                            <Link
                              to="/products"
                              className="flex items-center gap-2 px-4 py-3 text-sm text-neutral-700 hover:bg-neutral-100 transition-colors"
                              onClick={() => setProductMenuOpen(false)}
                            >
                              <Package size={18} className="text-primary-600" />
                              <span>Manajemen Produk</span>
                            </Link>

                            <Link
                              to="/categories"
                              className="flex items-center gap-2 px-4 py-3 text-sm text-neutral-700 hover:bg-neutral-100 transition-colors"
                              onClick={() => setProductMenuOpen(false)}
                            >
                              <Tag size={18} className="text-primary-600" />
                              <span>Manajemen Kategori</span>
                            </Link>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Cabang & User Dropdown */}
                    <div className="relative" ref={adminMenuRef}>
                      <button
                        onClick={() => setAdminMenuOpen(!adminMenuOpen)}
                        className="flex items-center gap-2 text-neutral-700 hover:text-primary-600 py-2 focus:outline-none"
                      >
                        <Building size={18} className="text-primary-600" />
                        <span>Cabang & User</span>
                        <ChevronDown size={16} className={`transition-transform ${adminMenuOpen ? 'rotate-180' : ''}`} />
                      </button>

                      {adminMenuOpen && (
                        <div className="absolute left-0 mt-1 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50">
                          <div className="py-1" role="menu" aria-orientation="vertical">
                            <Link
                              to="/branches"
                              className="flex items-center gap-2 px-4 py-3 text-sm text-neutral-700 hover:bg-neutral-100 transition-colors"
                              onClick={() => setAdminMenuOpen(false)}
                            >
                              <Building size={18} className="text-primary-600" />
                              <span>Manajemen Cabang</span>
                            </Link>

                            <Link
                              to="/cashier-management"
                              className="flex items-center gap-2 px-4 py-3 text-sm text-neutral-700 hover:bg-neutral-100 transition-colors"
                              onClick={() => setAdminMenuOpen(false)}
                            >
                              <Users size={18} className="text-primary-600" />
                              <span>Manajemen Kasir</span>
                            </Link>

                            <Link
                              to="/members"
                              className="flex items-center gap-2 px-4 py-3 text-sm text-neutral-700 hover:bg-neutral-100 transition-colors"
                              onClick={() => setAdminMenuOpen(false)}
                            >
                              <Users size={18} className="text-primary-600" />
                              <span>Manajemen Member</span>
                            </Link>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Konfigurasi Dropdown */}
                    <div className="relative" ref={configMenuRef}>
                      <button
                        onClick={() => setConfigMenuOpen(!configMenuOpen)}
                        className="flex items-center gap-2 text-neutral-700 hover:text-primary-600 py-2 focus:outline-none"
                      >
                        <Settings size={18} className="text-primary-600" />
                        <span>Konfigurasi</span>
                        <ChevronDown size={16} className={`transition-transform ${configMenuOpen ? 'rotate-180' : ''}`} />
                      </button>

                      {configMenuOpen && (
                        <div className="absolute left-0 mt-1 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50">
                          <div className="py-1" role="menu" aria-orientation="vertical">
                            <Link
                              to="/payment-methods"
                              className="flex items-center gap-2 px-4 py-3 text-sm text-neutral-700 hover:bg-neutral-100 transition-colors"
                              onClick={() => setConfigMenuOpen(false)}
                            >
                              <CreditCard size={18} className="text-primary-600" />
                              <span>Metode Pembayaran</span>
                            </Link>

                            <Link
                              to="/payment-gateway-config"
                              className="flex items-center gap-2 px-4 py-3 text-sm text-neutral-700 hover:bg-neutral-100 transition-colors"
                              onClick={() => setConfigMenuOpen(false)}
                            >
                              <CreditCardIcon size={18} className="text-primary-600" />
                              <span>Payment Gateway</span>
                            </Link>

                            <Link
                              to="/store-config"
                              className="flex items-center gap-2 px-4 py-3 text-sm text-neutral-700 hover:bg-neutral-100 transition-colors"
                              onClick={() => setConfigMenuOpen(false)}
                            >
                              <Settings size={18} className="text-primary-600" />
                              <span>Konfigurasi Toko</span>
                            </Link>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </>
              )}
            </nav>
          </div>

          <div className="relative">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="flex items-center gap-2 text-neutral-700 hover:text-primary-600 focus:outline-none"
            >
              <span className="text-sm font-medium hidden md:inline-block">{userName}</span>
              <div className="w-8 h-8 rounded-full bg-primary-100 flex items-center justify-center text-primary-600">
                <User size={18} />
              </div>
              <Menu size={20} />
            </button>

            {/* Dropdown Menu - Simplified */}
            {isMenuOpen && (
              <div className="absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50">
                <div className="py-1" role="menu" aria-orientation="vertical">
                  {/* Mobile-only navigation links */}
                  <div className="md:hidden">
                    {/* Order link - only for cashiers */}
                    {userRole === 'cashier' && (
                      <Link
                        to="/dashboard"
                        className="flex items-center gap-2 px-4 py-3 text-sm text-neutral-700 hover:bg-neutral-100 transition-colors"
                        role="menuitem"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        <Package size={18} className="text-primary-600" />
                        <span>Order</span>
                      </Link>
                    )}

                    {/* Dashboard Summary - only for admin and branch */}
                    {(userRole === 'admin' || userRole === 'branch') && (
                      <Link
                        to="/dashboard-summary"
                        className="flex items-center gap-2 px-4 py-3 text-sm text-neutral-700 hover:bg-neutral-100 transition-colors"
                        role="menuitem"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        <BarChart3 size={18} className="text-primary-600" />
                        <span>Dashboard</span>
                      </Link>
                    )}

                    <Link
                      to="/transactions"
                      className="flex items-center gap-2 px-4 py-3 text-sm text-neutral-700 hover:bg-neutral-100 transition-colors"
                      role="menuitem"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      <History size={18} className="text-primary-600" />
                      <span>Riwayat Transaksi</span>
                    </Link>

                    {userRole !== 'cashier' && (
                      <Link
                        to="/reports"
                        className="flex items-center gap-2 px-4 py-3 text-sm text-neutral-700 hover:bg-neutral-100 transition-colors"
                        role="menuitem"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        <BarChart2 size={18} className="text-primary-600" />
                        <span>Laporan</span>
                      </Link>
                    )}

                    {/* Expenses - only for admin and branch in mobile menu */}
                    {(userRole === 'admin' || userRole === 'branch') && (
                      <Link
                        to="/expenses"
                        className="flex items-center gap-2 px-4 py-3 text-sm text-neutral-700 hover:bg-neutral-100 transition-colors"
                        role="menuitem"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        <DollarSign size={18} className="text-primary-600" />
                        <span>Pengeluaran</span>
                      </Link>
                    )}

                    {/* Product Management for branch users in mobile menu */}
                    {userRole === 'branch' && (
                      <Link
                        to="/products"
                        className="flex items-center gap-2 px-4 py-3 text-sm text-neutral-700 hover:bg-neutral-100 transition-colors"
                        role="menuitem"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        <Package size={18} className="text-primary-600" />
                        <span>Produk</span>
                      </Link>
                    )}

                    {/* Category Management for branch users in mobile menu */}
                    {userRole === 'branch' && (
                      <Link
                        to="/categories"
                        className="flex items-center gap-2 px-4 py-3 text-sm text-neutral-700 hover:bg-neutral-100 transition-colors"
                        role="menuitem"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        <Tag size={18} className="text-primary-600" />
                        <span>Kategori</span>
                      </Link>
                    )}

                    {/* Add Cashier Management link for branch users in mobile menu */}
                    {userRole === 'branch' && (
                      <Link
                        to="/cashier-management"
                        className="flex items-center gap-2 px-4 py-3 text-sm text-neutral-700 hover:bg-neutral-100 transition-colors"
                        role="menuitem"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        <Users size={18} className="text-primary-600" />
                        <span>Manajemen Kasir</span>
                      </Link>
                    )}

                    {isAdmin && (
                      <>
                        <Link
                          to="/products"
                          className="flex items-center gap-2 px-4 py-3 text-sm text-neutral-700 hover:bg-neutral-100 transition-colors"
                          role="menuitem"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          <Package size={18} className="text-primary-600" />
                          <span>Manajemen Produk</span>
                        </Link>

                        <Link
                          to="/categories"
                          className="flex items-center gap-2 px-4 py-3 text-sm text-neutral-700 hover:bg-neutral-100 transition-colors"
                          role="menuitem"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          <Tag size={18} className="text-primary-600" />
                          <span>Manajemen Kategori</span>
                        </Link>

                        <Link
                          to="/branches"
                          className="flex items-center gap-2 px-4 py-3 text-sm text-neutral-700 hover:bg-neutral-100 transition-colors"
                          role="menuitem"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          <Building size={18} className="text-primary-600" />
                          <span>Manajemen Cabang</span>
                        </Link>

                        {/* <Link
                          to="/users"
                          className="flex items-center gap-2 px-4 py-3 text-sm text-neutral-700 hover:bg-neutral-100 transition-colors"
                          role="menuitem"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          <User size={18} className="text-primary-600" />
                          <span>Manajemen User</span>
                        </Link> */}

                        <Link
                          to="/payment-methods"
                          className="flex items-center gap-2 px-4 py-3 text-sm text-neutral-700 hover:bg-neutral-100 transition-colors"
                          role="menuitem"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          <CreditCard size={18} className="text-primary-600" />
                          <span>Metode Pembayaran</span>
                        </Link>

                        <Link
                          to="/payment-gateway-config"
                          className="flex items-center gap-2 px-4 py-3 text-sm text-neutral-700 hover:bg-neutral-100 transition-colors"
                          role="menuitem"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          <CreditCardIcon size={18} className="text-primary-600" />
                          <span>Payment Gateway</span>
                        </Link>

                        <Link
                          to="/store-config"
                          className="flex items-center gap-2 px-4 py-3 text-sm text-neutral-700 hover:bg-neutral-100 transition-colors"
                          role="menuitem"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          <Settings size={18} className="text-primary-600" />
                          <span>Konfigurasi Toko</span>
                        </Link>

                        <Link
                          to="/members"
                          className="flex items-center gap-2 px-4 py-3 text-sm text-neutral-700 hover:bg-neutral-100 transition-colors"
                          role="menuitem"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          <Users size={18} className="text-primary-600" />
                          <span>Manajemen Member</span>
                        </Link>

                        <Link
                          to="/cashier-management"
                          className="flex items-center gap-2 px-4 py-3 text-sm text-neutral-700 hover:bg-neutral-100 transition-colors"
                          role="menuitem"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          <Users size={18} className="text-primary-600" />
                          <span>Manajemen Kasir</span>
                        </Link>
                      </>
                    )}

                    <div className="border-t border-neutral-100 my-1"></div>
                  </div>

                  <Link
                    to="/change-password"
                    className="flex items-center gap-2 px-4 py-3 text-sm text-neutral-700 hover:bg-neutral-100 transition-colors"
                    role="menuitem"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <Lock size={18} className="text-primary-600" />
                    <span>Ubah Password</span>
                  </Link>

                  <button
                    onClick={handleLogoutClick}
                    className="flex items-center gap-2 px-4 py-3 text-sm text-red-600 hover:bg-red-50 transition-colors w-full text-left"
                    role="menuitem"
                  >
                    <LogOut size={18} />
                    <span>Logout</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Logout Confirmation Modal */}
      {showLogoutConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 animate-fade-in">
            <div className="flex items-center gap-3 mb-4">
              <AlertTriangle size={24} className="text-red-500" />
              <h3 className="text-lg font-medium text-neutral-900">Konfirmasi Logout</h3>
            </div>
            <p className="text-neutral-600 mb-6">
              Apakah Anda yakin ingin keluar dari aplikasi?
            </p>
            <div className="flex justify-end gap-3">
              <button
                onClick={cancelLogout}
                className="px-4 py-2 bg-neutral-100 text-neutral-700 rounded-md hover:bg-neutral-200 focus:outline-none focus:ring-2 focus:ring-neutral-500"
              >
                Batal
              </button>
              <button
                onClick={confirmLogout}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;







































